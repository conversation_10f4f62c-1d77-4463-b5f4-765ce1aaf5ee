<?php include '_header.php'; 

$pdo = db();
$id = (int)($_GET['id'] ?? 0);
$error = '';
$success = '';

// Get the unit
$stmt = $pdo->prepare("SELECT * FROM units WHERE id = ?");
$stmt->execute([$id]);
$unit = $stmt->fetch();

if (!$unit) {
    echo '<div class="alert alert-danger">Unit not found.</div>';
    include '_footer.php';
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $abbreviation = trim($_POST['abbreviation'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (empty($name)) {
        $error = 'Unit name is required.';
    } else {
        try {
            $stmt = $pdo->prepare("UPDATE units SET name = ?, abbreviation = ?, description = ? WHERE id = ?");
            $stmt->execute([$name, $abbreviation, $description, $id]);
            $success = 'Unit updated successfully!';
            // Refresh unit data
            $unit['name'] = $name;
            $unit['abbreviation'] = $abbreviation;
            $unit['description'] = $description;
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry
                $error = 'A unit with this name already exists.';
            } else {
                $error = 'An error occurred while updating the unit.';
            }
        }
    }
}

// Check if unit is used by any items
$stmt = $pdo->prepare("SELECT COUNT(*) as item_count FROM items WHERE unit_id = ?");
$stmt->execute([$id]);
$usage = $stmt->fetch();
$itemCount = (int)$usage['item_count'];
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Edit Unit #<?php echo (int)$unit['id']; ?></h3>
  <a href="units.php" class="btn btn-secondary">Back to Units</a>
</div>

<?php if ($error): ?>
  <div class="alert alert-danger"><?php echo h($error); ?></div>
<?php endif; ?>

<?php if ($success): ?>
  <div class="alert alert-success"><?php echo h($success); ?></div>
<?php endif; ?>

<form method="post" class="bg-white p-4 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-6">
      <label class="form-label">Unit Name *</label>
      <input name="name" required class="form-control" value="<?php echo h($unit['name']); ?>" placeholder="e.g., Pieces, Kilograms, Liters">
      <div class="form-text">Enter a unique unit name</div>
    </div>
    <div class="col-md-3">
      <label class="form-label">Abbreviation</label>
      <input name="abbreviation" class="form-control" value="<?php echo h($unit['abbreviation']); ?>" placeholder="e.g., pcs, kg, L">
      <div class="form-text">Short form of the unit</div>
    </div>
    <div class="col-12">
      <label class="form-label">Description</label>
      <textarea name="description" class="form-control" rows="3" placeholder="Optional description about this unit"><?php echo h($unit['description']); ?></textarea>
      <div class="form-text">Optional description or notes about this unit</div>
    </div>
  </div>
  
  <div class="mt-4 d-flex justify-content-between">
    <div>
      <button type="submit" class="btn btn-primary">Update Unit</button>
      <a href="units.php" class="btn btn-secondary">Cancel</a>
    </div>
    
    <?php if ($itemCount > 0): ?>
      <div class="text-muted small">
        <i class="fas fa-info-circle"></i> This unit is used by <?php echo $itemCount; ?> item(s)
      </div>
    <?php else: ?>
      <form method="post" action="unit_delete.php" onsubmit="return confirm('Delete this unit? This action cannot be undone.');">
        <input type="hidden" name="id" value="<?php echo (int)$unit['id']; ?>">
        <button class="btn btn-outline-danger">Delete Unit</button>
      </form>
    <?php endif; ?>
  </div>
</form>

<?php include '_footer.php'; ?>
