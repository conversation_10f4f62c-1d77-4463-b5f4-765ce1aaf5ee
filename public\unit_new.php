<?php include '_header.php'; 

$pdo = db();
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $abbreviation = trim($_POST['abbreviation'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (empty($name)) {
        $error = 'Unit name is required.';
    } else {
        try {
            $stmt = $pdo->prepare("INSERT INTO units (name, abbreviation, description) VALUES (?, ?, ?)");
            $stmt->execute([$name, $abbreviation, $description]);
            $success = 'Unit added successfully!';
            // Clear form
            $name = '';
            $abbreviation = '';
            $description = '';
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry
                $error = 'A unit with this name already exists.';
            } else {
                $error = 'An error occurred while saving the unit.';
            }
        }
    }
}
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Add Unit</h3>
  <a href="units.php" class="btn btn-secondary">Back to Units</a>
</div>

<?php if ($error): ?>
  <div class="alert alert-danger"><?php echo h($error); ?></div>
<?php endif; ?>

<?php if ($success): ?>
  <div class="alert alert-success"><?php echo h($success); ?></div>
<?php endif; ?>

<form method="post" class="bg-white p-4 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-6">
      <label class="form-label">Unit Name *</label>
      <input name="name" required class="form-control" value="<?php echo h($name ?? ''); ?>" placeholder="e.g., Pieces, Kilograms, Liters">
      <div class="form-text">Enter a unique unit name</div>
    </div>
    <div class="col-md-3">
      <label class="form-label">Abbreviation</label>
      <input name="abbreviation" class="form-control" value="<?php echo h($abbreviation ?? ''); ?>" placeholder="e.g., pcs, kg, L">
      <div class="form-text">Short form of the unit</div>
    </div>
    <div class="col-12">
      <label class="form-label">Description</label>
      <textarea name="description" class="form-control" rows="3" placeholder="Optional description about this unit"><?php echo h($description ?? ''); ?></textarea>
      <div class="form-text">Optional description or notes about this unit</div>
    </div>
  </div>
  
  <div class="mt-4">
    <button type="submit" class="btn btn-primary">Save Unit</button>
    <a href="units.php" class="btn btn-secondary">Cancel</a>
  </div>
</form>

<div class="mt-4">
  <div class="card">
    <div class="card-body">
      <h6 class="card-title">Tips</h6>
      <ul class="mb-0 small text-muted">
        <li>Unit names must be unique</li>
        <li>Use standard units like "Pieces", "Kilograms", "Liters", "Meters"</li>
        <li>Add abbreviations for easier display (e.g., "pcs", "kg", "L")</li>
        <li>Include descriptions to clarify the unit's purpose</li>
      </ul>
    </div>
  </div>
</div>

<?php include '_footer.php'; ?>
