<?php
/**
 * Database Update Script
 * This script updates the database schema and migrates existing data
 * Run this script once to upgrade from the old schema to the new one
 */

require_once __DIR__ . '/db.php';

try {
    $pdo = db();
    $pdo->beginTransaction();
    
    echo "Starting database update...\n";
    
    // Check if we need to create the lookup tables
    $stmt = $pdo->query("SHOW TABLES LIKE 'brands'");
    if ($stmt->rowCount() == 0) {
        echo "Creating lookup tables...\n";
        
        // Create brands table
        $pdo->exec("CREATE TABLE brands (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(120) NOT NULL UNIQUE,
            description TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB");
        
        // Create conditions table
        $pdo->exec("CREATE TABLE conditions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VA<PERSON>HA<PERSON>(80) NOT NULL UNIQUE,
            description TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB");
        
        // Create locations table
        $pdo->exec("CREATE TABLE locations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(160) NOT NULL UNIQUE,
            description TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB");
        
        // Create units table
        $pdo->exec("CREATE TABLE units (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(20) NOT NULL UNIQUE,
            abbreviation VARCHAR(10) NULL,
            description TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB");
        
        echo "Lookup tables created.\n";
    }
    
    // Insert default data
    echo "Inserting default data...\n";
    
    $pdo->exec("INSERT IGNORE INTO brands (name, description) VALUES 
        ('Generic', 'Generic or unbranded items'),
        ('Apple', 'Apple Inc. products'),
        ('Samsung', 'Samsung Electronics'),
        ('Dell', 'Dell Technologies'),
        ('HP', 'Hewlett-Packard'),
        ('Lenovo', 'Lenovo Group')");
    
    $pdo->exec("INSERT IGNORE INTO conditions (name, description) VALUES 
        ('New', 'Brand new, unused condition'),
        ('Like New', 'Excellent condition, minimal use'),
        ('Good', 'Good working condition with minor wear'),
        ('Fair', 'Working condition with noticeable wear'),
        ('Poor', 'Functional but significant wear'),
        ('Refurbished', 'Professionally restored to working condition'),
        ('Damaged', 'Not working or significantly damaged')");
    
    $pdo->exec("INSERT IGNORE INTO locations (name, description) VALUES 
        ('Main Warehouse', 'Primary storage facility'),
        ('Office A', 'Main office location'),
        ('Office B', 'Secondary office location'),
        ('Store Front', 'Retail store location'),
        ('Storage Room', 'General storage area'),
        ('IT Department', 'Information Technology department')");
    
    $pdo->exec("INSERT IGNORE INTO units (name, abbreviation, description) VALUES 
        ('Pieces', 'pcs', 'Individual items or pieces'),
        ('Boxes', 'box', 'Items sold by the box'),
        ('Kilograms', 'kg', 'Weight measurement in kilograms'),
        ('Liters', 'L', 'Volume measurement in liters'),
        ('Meters', 'm', 'Length measurement in meters'),
        ('Sets', 'set', 'Items sold as complete sets'),
        ('Pairs', 'pair', 'Items sold in pairs')");
    
    // Check if items table needs to be updated
    $stmt = $pdo->query("SHOW COLUMNS FROM items LIKE 'brand_id'");
    if ($stmt->rowCount() == 0) {
        echo "Updating items table structure...\n";
        
        // Add new foreign key columns
        $pdo->exec("ALTER TABLE items 
            ADD COLUMN brand_id INT NULL,
            ADD COLUMN condition_id INT NULL,
            ADD COLUMN location_id INT NULL,
            ADD COLUMN unit_id INT NULL");
        
        echo "Migrating existing data...\n";
        
        // Migrate existing brand data
        $pdo->exec("INSERT IGNORE INTO brands (name) 
            SELECT DISTINCT brand FROM items WHERE brand IS NOT NULL AND brand != ''");
        
        $pdo->exec("UPDATE items i 
            JOIN brands b ON i.brand = b.name 
            SET i.brand_id = b.id 
            WHERE i.brand IS NOT NULL AND i.brand != ''");
        
        // Migrate existing condition data
        $pdo->exec("INSERT IGNORE INTO conditions (name) 
            SELECT DISTINCT condition_status FROM items WHERE condition_status IS NOT NULL AND condition_status != ''");
        
        $pdo->exec("UPDATE items i 
            JOIN conditions c ON i.condition_status = c.name 
            SET i.condition_id = c.id 
            WHERE i.condition_status IS NOT NULL AND i.condition_status != ''");
        
        // Migrate existing location data
        $pdo->exec("INSERT IGNORE INTO locations (name) 
            SELECT DISTINCT location FROM items WHERE location IS NOT NULL AND location != ''");
        
        $pdo->exec("UPDATE items i 
            JOIN locations l ON i.location = l.name 
            SET i.location_id = l.id 
            WHERE i.location IS NOT NULL AND i.location != ''");
        
        // Migrate existing unit data
        $pdo->exec("INSERT IGNORE INTO units (name, abbreviation) 
            SELECT DISTINCT unit, unit FROM items WHERE unit IS NOT NULL AND unit != ''");
        
        $pdo->exec("UPDATE items i 
            JOIN units u ON i.unit = u.name 
            SET i.unit_id = u.id 
            WHERE i.unit IS NOT NULL AND i.unit != ''");
        
        // Set default unit for items without a unit
        $pdo->exec("UPDATE items SET unit_id = (SELECT id FROM units WHERE name = 'Pieces' LIMIT 1) 
            WHERE unit_id IS NULL");
        
        // Add foreign key constraints
        $pdo->exec("ALTER TABLE items 
            ADD FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE SET NULL,
            ADD FOREIGN KEY (condition_id) REFERENCES conditions(id) ON DELETE SET NULL,
            ADD FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE SET NULL,
            ADD FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE SET NULL");
        
        echo "Data migration completed.\n";
        
        // Drop old columns (commented out for safety)
        // echo "Dropping old columns...\n";
        // $pdo->exec("ALTER TABLE items DROP COLUMN brand, DROP COLUMN condition_status, DROP COLUMN location, DROP COLUMN unit");
    }
    
    // Update the view
    echo "Updating view...\n";
    $pdo->exec("CREATE OR REPLACE VIEW v_item_stock AS
        SELECT
            i.id as item_id,
            i.name,
            b.name as brand,
            i.model,
            c.name as condition_status,
            l.name as location,
            u.name as unit,
            u.abbreviation as unit_abbr,
            COALESCE(SUM(CASE WHEN t.tx_type='in' THEN t.qty WHEN t.tx_type='adjust' THEN t.qty ELSE 0 END),0)
            - COALESCE(SUM(CASE WHEN t.tx_type='out' THEN t.qty ELSE 0 END),0) AS stock_qty
        FROM items i
        LEFT JOIN brands b ON i.brand_id = b.id
        LEFT JOIN conditions c ON i.condition_id = c.id
        LEFT JOIN locations l ON i.location_id = l.id
        LEFT JOIN units u ON i.unit_id = u.id
        LEFT JOIN inventory_transactions t ON t.item_id = i.id
        GROUP BY i.id, i.name, b.name, i.model, c.name, l.name, u.name, u.abbreviation");
    
    $pdo->commit();
    echo "Database update completed successfully!\n";
    
} catch (Exception $e) {
    $pdo->rollback();
    echo "Error updating database: " . $e->getMessage() . "\n";
    exit(1);
}
?>
