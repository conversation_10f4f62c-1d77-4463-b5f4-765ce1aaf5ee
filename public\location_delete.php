<?php include '_header.php'; 

$pdo = db();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id = (int)($_POST['id'] ?? 0);
    
    if ($id > 0) {
        try {
            // Check if location is used by any items
            $stmt = $pdo->prepare("SELECT COUNT(*) as item_count FROM items WHERE location_id = ?");
            $stmt->execute([$id]);
            $usage = $stmt->fetch();
            
            if ((int)$usage['item_count'] > 0) {
                $error = 'Cannot delete this location because it is used by ' . (int)$usage['item_count'] . ' item(s).';
            } else {
                $stmt = $pdo->prepare("DELETE FROM locations WHERE id = ?");
                $stmt->execute([$id]);
                
                if ($stmt->rowCount() > 0) {
                    $success = 'Location deleted successfully.';
                } else {
                    $error = 'Location not found or already deleted.';
                }
            }
        } catch (PDOException $e) {
            $error = 'An error occurred while deleting the location.';
        }
    } else {
        $error = 'Invalid location ID.';
    }
} else {
    redirect('locations.php');
}
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Delete Location</h3>
  <a href="locations.php" class="btn btn-secondary">Back to Locations</a>
</div>

<?php if (isset($error)): ?>
  <div class="alert alert-danger">
    <?php echo h($error); ?>
  </div>
<?php endif; ?>

<?php if (isset($success)): ?>
  <div class="alert alert-success">
    <?php echo h($success); ?>
  </div>
<?php endif; ?>

<div class="mt-4">
  <a href="locations.php" class="btn btn-primary">Return to Locations</a>
</div>

<?php include '_footer.php'; ?>
