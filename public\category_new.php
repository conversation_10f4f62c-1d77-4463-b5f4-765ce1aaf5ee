<?php include '_header.php'; 

$pdo = db();
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (empty($name)) {
        $error = 'Category name is required.';
    } else {
        try {
            $stmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
            $stmt->execute([$name, $description]);
            $success = 'Category added successfully!';
            // Clear form
            $name = '';
            $description = '';
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry
                $error = 'A category with this name already exists.';
            } else {
                $error = 'An error occurred while saving the category.';
            }
        }
    }
}
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Add Equipment Category</h3>
  <a href="categories.php" class="btn btn-secondary">Back to Categories</a>
</div>

<?php if ($error): ?>
  <div class="alert alert-danger"><?php echo h($error); ?></div>
<?php endif; ?>

<?php if ($success): ?>
  <div class="alert alert-success"><?php echo h($success); ?></div>
<?php endif; ?>

<form method="post" class="bg-white p-4 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-6">
      <label class="form-label">Category Name *</label>
      <input name="name" required class="form-control" value="<?php echo h($name ?? ''); ?>" placeholder="e.g., Computers, Networking, Printers">
      <div class="form-text">Enter a unique category name</div>
    </div>
    <div class="col-12">
      <label class="form-label">Description</label>
      <textarea name="description" class="form-control" rows="3" placeholder="Describe what types of equipment belong in this category"><?php echo h($description ?? ''); ?></textarea>
      <div class="form-text">Optional description to help identify what equipment belongs in this category</div>
    </div>
  </div>
  
  <div class="mt-4">
    <button type="submit" class="btn btn-primary">Save Category</button>
    <a href="categories.php" class="btn btn-secondary">Cancel</a>
  </div>
</form>

<div class="mt-4">
  <div class="card">
    <div class="card-body">
      <h6 class="card-title">Category Examples</h6>
      <div class="row">
        <div class="col-md-6">
          <ul class="mb-0 small">
            <li><strong>Computers</strong> - Desktops, laptops, workstations</li>
            <li><strong>Networking</strong> - Routers, switches, access points</li>
            <li><strong>Peripherals</strong> - Keyboards, mice, webcams</li>
            <li><strong>Monitors</strong> - Computer displays and screens</li>
          </ul>
        </div>
        <div class="col-md-6">
          <ul class="mb-0 small">
            <li><strong>Printers</strong> - Printers, scanners, MFDs</li>
            <li><strong>Storage</strong> - Hard drives, SSDs, USB drives</li>
            <li><strong>Audio/Video</strong> - Projectors, cameras, speakers</li>
            <li><strong>Mobile Devices</strong> - Tablets, smartphones</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<?php include '_footer.php'; ?>
