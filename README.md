# Enhanced PHP + MySQL Inventory App

Enhanced inventory management system with **lookup tables**, **advanced filtering**, and **improved data management**.

Features include:
- ✅ Add items with dropdown selections for brands, conditions, locations, and units
- ✅ Manage lookup data (brands, conditions, locations, units) with dedicated CRUD pages
- ✅ Advanced search and filtering capabilities
- ✅ Inventory tracking (IN/OUT/ADJUST transactions)
- ✅ Responsive design with improved navigation

## Quick Start

### For New Installations
1. Create DB and tables:
   ```sql
   CREATE DATABASE inventory_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   USE inventory_db;
   SOURCE init.sql;
   ```

2. Configure DB credentials in `config.php`.

3. Serve the `public/` folder from PHP (built-in server example):
   ```bash
   php -S 0.0.0.0:8000 -t public
   ```

4. Navigate to `http://localhost:8000` to use the app.

### For Existing Installations (Upgrade)
1. **Backup your database first!**
2. Run the update script:
   ```bash
   php update_database.php
   ```
3. This will create lookup tables and migrate existing data to the new structure.

## Bulk Import
Use `public/import_csv.php` to import from a CSV with columns similar to your file:
- ITEM, BRAND, MODEL (or Unnamed: 3), QUANT, CONDITION, LOCATION, UNIT_COST.

Each row creates an item and an initial **ADJUST** transaction with `QUANT` if provided.

## New Features

### 1. Lookup Tables Management
- **Brands**: Manage product brands with descriptions
- **Conditions**: Define item conditions (New, Used, Good, etc.)
- **Locations**: Organize storage locations
- **Units**: Define measurement units (pieces, kg, liters, etc.)

Access via the "Manage" dropdown in the navigation menu.

### 2. Enhanced Item Forms
- Dropdown selections instead of text inputs
- Quick links to manage lookup data
- Better data validation and consistency

### 3. Advanced Search and Filtering
- Search across item names, brands, and models
- Filter by brand, condition, location, and unit
- Clear filter options with results count

### 4. Improved Navigation
- Responsive design with collapsible menu
- Organized management section
- Mobile-friendly interface

## Usage Tips

1. **Set up lookup data first**: Before adding items, configure your brands, conditions, locations, and units
2. **Use the filters**: Quickly find items using the search and filter options on the main page
3. **Manage lookup data**: Use the "Manage" menu to add/edit brands, conditions, locations, and units
4. **Consistent naming**: Keep lookup entries consistent for better organization

## Notes
- Stock is calculated from transactions view `v_item_stock`
- Edit items from the list, record new transactions (receive, issue, adjust)
- Foreign key relationships ensure data integrity
- Lookup entries cannot be deleted if they're in use by items
- Export/reporting can be added easily later
