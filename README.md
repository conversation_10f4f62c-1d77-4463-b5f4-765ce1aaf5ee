# Hardware Equipment Inventory Management System

Comprehensive **hardware inventory management** system designed for IT departments, offices, and organizations managing computer equipment, networking devices, peripherals, and other hardware assets.

## 🖥️ **Hardware-Focused Features**
- ✅ **Equipment Categories**: Computers, Networking, Printers, Peripherals, Storage, etc.
- ✅ **Hardware-Specific Fields**: Serial numbers, asset tags, warranty dates
- ✅ **Comprehensive Brand Database**: Major hardware manufacturers pre-loaded
- ✅ **IT-Focused Locations**: Server rooms, IT storage, deployment staging
- ✅ **Advanced Search**: Find equipment by name, brand, model, serial number, or asset tag
- ✅ **Professional Interface**: Clean, responsive design optimized for hardware management
- ✅ **Inventory Tracking**: Track equipment IN/OUT/ADJUST transactions

## Quick Start

### For New Installations
1. Create DB and tables:
   ```sql
   CREATE DATABASE inventory_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   USE inventory_db;
   SOURCE init.sql;
   ```

2. Configure DB credentials in `config.php`.

3. Serve the `public/` folder from PHP (built-in server example):
   ```bash
   php -S 0.0.0.0:8000 -t public
   ```

4. Navigate to `http://localhost:8000` to use the app.

### For Existing Installations (Upgrade)
1. **Backup your database first!**
2. Run the update script:
   ```bash
   php update_database.php
   ```
3. This will create lookup tables and migrate existing data to the new structure.

## Bulk Import
Use `public/import_csv.php` to import from a CSV with columns similar to your file:
- ITEM, BRAND, MODEL (or Unnamed: 3), QUANT, CONDITION, LOCATION, UNIT_COST.

Each row creates an item and an initial **ADJUST** transaction with `QUANT` if provided.

## 🔧 **Hardware Management Features**

### 1. Equipment Categories
- **Computers**: Desktop computers, laptops, workstations
- **Networking**: Routers, switches, access points, cables
- **Peripherals**: Keyboards, mice, speakers, webcams
- **Printers**: Printers, scanners, multifunction devices
- **Storage**: Hard drives, SSDs, USB drives, NAS devices
- **Servers**: Server hardware and rack equipment
- **Mobile Devices**: Tablets, smartphones, mobile accessories
- **Audio/Video**: Projectors, cameras, microphones, headsets

### 2. Hardware-Specific Data Management
- **Categories**: Organize equipment by type
- **Brands**: 50+ pre-loaded hardware manufacturers
- **Conditions**: Equipment condition tracking
- **Locations**: IT-focused location management
- **Units**: Appropriate measurement units

Access via the "Manage" dropdown in the navigation menu.

### 3. Enhanced Equipment Forms
- **Category Selection**: Choose from predefined equipment types
- **Hardware Fields**: Serial number, asset tag, warranty date tracking
- **Dropdown Selections**: Consistent data entry with validation
- **Quick Management Links**: Direct access to manage lookup data

### 4. Advanced Search and Filtering
- **Multi-Field Search**: Equipment names, brands, models, serial numbers, asset tags
- **Category Filtering**: Filter by equipment type
- **Comprehensive Filters**: Brand, condition, location, and unit filters
- **Results Management**: Clear filter options with item counts

### 5. Professional Interface
- **Hardware-Focused Design**: Optimized for IT equipment management
- **Responsive Layout**: Works on desktop, tablet, and mobile
- **Equipment-Centric Navigation**: Clear organization for hardware assets
- **Asset Information Display**: Serial numbers, asset tags, warranty status

## 💡 **Usage Tips for Hardware Management**

1. **Start with Categories**: Set up equipment categories that match your organization's hardware types
2. **Configure Locations**: Define locations specific to your IT environment (server rooms, storage areas, etc.)
3. **Use Asset Tags**: Implement a consistent asset tagging system for better tracking
4. **Track Serial Numbers**: Always record serial numbers for warranty and support purposes
5. **Monitor Warranties**: Use warranty date fields to track equipment lifecycle
6. **Leverage Filtering**: Use category and brand filters to quickly find specific equipment types
7. **Consistent Data Entry**: Use the dropdown selections to maintain data consistency

## Notes
- Stock is calculated from transactions view `v_item_stock`
- Edit items from the list, record new transactions (receive, issue, adjust)
- Foreign key relationships ensure data integrity
- Lookup entries cannot be deleted if they're in use by items
- Export/reporting can be added easily later
