# PHP + MySQL Inventory App

Minimal inventory app to **add items** and **inventory them** (IN/OUT/ADJUST).

## Quick Start

1. Create DB and tables:
   ```sql
   CREATE DATABASE inventory_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   USE inventory_db;
   SOURCE init.sql;
   ```

2. Configure DB credentials in `config.php`.

3. Serve the `public/` folder from P<PERSON> (built-in server example):
   ```bash
   php -S 0.0.0.0:8000 -t public
   ```

4. Navigate to `http://localhost:8000` to use the app.

## Bulk Import
Use `public/import_csv.php` to import from a CSV with columns similar to your file:
- ITEM, BRAND, MODEL (or Unnamed: 3), QUANT, CONDIT<PERSON>, LOCATION, UNIT_COST.

Each row creates an item and an initial **ADJUST** transaction with `QUANT` if provided.

## Notes
- Stock is calculated from transactions view `v_item_stock`.
- Edit items from the list, record new transactions (receive, issue, adjust).
- Export/reporting can be added easily later.
