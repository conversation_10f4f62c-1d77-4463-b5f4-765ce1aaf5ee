<?php include '_header.php'; 

$pdo = db();
$id = (int)($_GET['id'] ?? 0);
$error = '';
$success = '';

// Get the brand
$stmt = $pdo->prepare("SELECT * FROM brands WHERE id = ?");
$stmt->execute([$id]);
$brand = $stmt->fetch();

if (!$brand) {
    echo '<div class="alert alert-danger">Brand not found.</div>';
    include '_footer.php';
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (empty($name)) {
        $error = 'Brand name is required.';
    } else {
        try {
            $stmt = $pdo->prepare("UPDATE brands SET name = ?, description = ? WHERE id = ?");
            $stmt->execute([$name, $description, $id]);
            $success = 'Brand updated successfully!';
            // Refresh brand data
            $brand['name'] = $name;
            $brand['description'] = $description;
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry
                $error = 'A brand with this name already exists.';
            } else {
                $error = 'An error occurred while updating the brand.';
            }
        }
    }
}

// Check if brand is used by any items
$stmt = $pdo->prepare("SELECT COUNT(*) as item_count FROM items WHERE brand_id = ?");
$stmt->execute([$id]);
$usage = $stmt->fetch();
$itemCount = (int)$usage['item_count'];
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Edit Brand #<?php echo (int)$brand['id']; ?></h3>
  <a href="brands.php" class="btn btn-secondary">Back to Brands</a>
</div>

<?php if ($error): ?>
  <div class="alert alert-danger"><?php echo h($error); ?></div>
<?php endif; ?>

<?php if ($success): ?>
  <div class="alert alert-success"><?php echo h($success); ?></div>
<?php endif; ?>

<form method="post" class="bg-white p-4 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-6">
      <label class="form-label">Brand Name *</label>
      <input name="name" required class="form-control" value="<?php echo h($brand['name']); ?>" placeholder="e.g., Apple, Samsung, Dell">
      <div class="form-text">Enter a unique brand name</div>
    </div>
    <div class="col-12">
      <label class="form-label">Description</label>
      <textarea name="description" class="form-control" rows="3" placeholder="Optional description about this brand"><?php echo h($brand['description']); ?></textarea>
      <div class="form-text">Optional description or notes about this brand</div>
    </div>
  </div>
  
  <div class="mt-4 d-flex justify-content-between">
    <div>
      <button type="submit" class="btn btn-primary">Update Brand</button>
      <a href="brands.php" class="btn btn-secondary">Cancel</a>
    </div>
    
    <?php if ($itemCount > 0): ?>
      <div class="text-muted small">
        <i class="fas fa-info-circle"></i> This brand is used by <?php echo $itemCount; ?> item(s)
      </div>
    <?php else: ?>
      <form method="post" action="brand_delete.php" onsubmit="return confirm('Delete this brand? This action cannot be undone.');">
        <input type="hidden" name="id" value="<?php echo (int)$brand['id']; ?>">
        <button class="btn btn-outline-danger">Delete Brand</button>
      </form>
    <?php endif; ?>
  </div>
</form>

<div class="mt-4">
  <div class="card">
    <div class="card-body">
      <h6 class="card-title">Brand Information</h6>
      <div class="row">
        <div class="col-md-6">
          <small class="text-muted">Created:</small><br>
          <strong><?php echo date('M j, Y g:i A', strtotime($brand['created_at'])); ?></strong>
        </div>
        <div class="col-md-6">
          <small class="text-muted">Last Updated:</small><br>
          <strong><?php echo date('M j, Y g:i A', strtotime($brand['updated_at'])); ?></strong>
        </div>
      </div>
      <?php if ($itemCount > 0): ?>
        <div class="mt-2">
          <small class="text-muted">Items using this brand:</small><br>
          <strong><?php echo $itemCount; ?> item(s)</strong>
          <a href="index.php?brand_id=<?php echo $id; ?>" class="btn btn-sm btn-outline-primary ms-2">View Items</a>
        </div>
      <?php endif; ?>
    </div>
  </div>
</div>

<?php include '_footer.php'; ?>
