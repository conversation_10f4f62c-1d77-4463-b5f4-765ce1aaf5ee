<?php include '_header.php';

$pdo = db();
$search = $_GET['q'] ?? '';
$sql = "SELECT i.*, s.stock_qty
        FROM items i
        LEFT JOIN v_item_stock s ON s.item_id = i.id
        WHERE i.name LIKE :q1 OR i.brand LIKE :q2 OR i.model LIKE :q3
        ORDER BY i.updated_at DESC
        LIMIT 500";
$stmt = $pdo->prepare($sql);
$stmt->execute([
    ':q1' => '%' . $search . '%',
    ':q2' => '%' . $search . '%',
    ':q3' => '%' . $search . '%'
]);
$rows = $stmt->fetchAll();
?>
<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Items</h3>
  <form class="d-flex" method="get">
    <input class="form-control me-2" type="search" name="q" placeholder="Search..." value="<?php echo h($search); ?>">
    <button class="btn btn-outline-secondary" type="submit">Search</button>
  </form>
</div>

<div class="table-responsive bg-white rounded shadow-sm">
<table class="table table-sm table-striped align-middle mb-0">
  <thead class="table-light">
    <tr>
      <th>#</th>
      <th>Name</th>
      <th>Brand</th>
      <th>Model</th>
      <th>Condition</th>
      <th>Stock</th>
      <th>Location</th>
      <th></th>
    </tr>
  </thead>
  <tbody>
    <?php foreach ($rows as $r): ?>
      <tr>
        <td><?php echo (int)$r['id']; ?></td>
        <td><?php echo h($r['name']); ?></td>
        <td><?php echo h($r['brand']); ?></td>
        <td><?php echo h($r['model']); ?></td>
        <td><?php echo h($r['condition_status']); ?></td>
        <td><span class="badge bg-<?php echo ((int)$r['stock_qty']<=0?'danger':'primary'); ?>">
            <?php echo (int)($r['stock_qty'] ?? 0); ?></span></td>
        <td><?php echo h($r['location']); ?></td>
        <td class="text-nowrap">
          <a class="btn btn-sm btn-outline-primary" href="item_edit.php?id=<?php echo (int)$r['id']; ?>">Edit</a>
        </td>
      </tr>
    <?php endforeach; ?>
  </tbody>
</table>
</div>
<?php include '_footer.php'; ?>