<?php include '_header.php';

$pdo = db();

// Get filter parameters
$search = $_GET['q'] ?? '';
$brand_filter = $_GET['brand_id'] ?? '';
$condition_filter = $_GET['condition_id'] ?? '';
$location_filter = $_GET['location_id'] ?? '';
$unit_filter = $_GET['unit_id'] ?? '';

// Get lookup data for filters
$brands = $pdo->query("SELECT id, name FROM brands ORDER BY name ASC")->fetchAll();
$conditions = $pdo->query("SELECT id, name FROM conditions ORDER BY name ASC")->fetchAll();
$locations = $pdo->query("SELECT id, name FROM locations ORDER BY name ASC")->fetchAll();
$units = $pdo->query("SELECT id, name, abbreviation FROM units ORDER BY name ASC")->fetchAll();

// Build the query with filters
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(i.name LIKE :search OR i.model LIKE :search OR b.name LIKE :search)";
    $params[':search'] = '%' . $search . '%';
}

if (!empty($brand_filter)) {
    $where_conditions[] = "i.brand_id = :brand_id";
    $params[':brand_id'] = $brand_filter;
}

if (!empty($condition_filter)) {
    $where_conditions[] = "i.condition_id = :condition_id";
    $params[':condition_id'] = $condition_filter;
}

if (!empty($location_filter)) {
    $where_conditions[] = "i.location_id = :location_id";
    $params[':location_id'] = $location_filter;
}

if (!empty($unit_filter)) {
    $where_conditions[] = "i.unit_id = :unit_id";
    $params[':unit_id'] = $unit_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

$sql = "SELECT i.*, s.stock_qty, s.brand, s.condition_status, s.location, s.unit, s.unit_abbr
        FROM items i
        LEFT JOIN v_item_stock s ON s.item_id = i.id
        LEFT JOIN brands b ON i.brand_id = b.id
        $where_clause
        ORDER BY i.updated_at DESC
        LIMIT 500";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$rows = $stmt->fetchAll();
?>
<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Items</h3>
  <a href="item_new.php" class="btn btn-primary">Add Item</a>
</div>

<!-- Search and Filter Form -->
<form method="get" class="bg-white p-3 rounded shadow-sm mb-3">
  <div class="row g-3">
    <div class="col-md-4">
      <label class="form-label">Search</label>
      <input class="form-control" type="search" name="q" placeholder="Search items, brands, models..." value="<?php echo h($search); ?>">
    </div>
    <div class="col-md-2">
      <label class="form-label">Brand</label>
      <select name="brand_id" class="form-select">
        <option value="">All Brands</option>
        <?php foreach ($brands as $brand): ?>
          <option value="<?php echo (int)$brand['id']; ?>" <?php echo $brand_filter == $brand['id'] ? 'selected' : ''; ?>>
            <?php echo h($brand['name']); ?>
          </option>
        <?php endforeach; ?>
      </select>
    </div>
    <div class="col-md-2">
      <label class="form-label">Condition</label>
      <select name="condition_id" class="form-select">
        <option value="">All Conditions</option>
        <?php foreach ($conditions as $condition): ?>
          <option value="<?php echo (int)$condition['id']; ?>" <?php echo $condition_filter == $condition['id'] ? 'selected' : ''; ?>>
            <?php echo h($condition['name']); ?>
          </option>
        <?php endforeach; ?>
      </select>
    </div>
    <div class="col-md-2">
      <label class="form-label">Location</label>
      <select name="location_id" class="form-select">
        <option value="">All Locations</option>
        <?php foreach ($locations as $location): ?>
          <option value="<?php echo (int)$location['id']; ?>" <?php echo $location_filter == $location['id'] ? 'selected' : ''; ?>>
            <?php echo h($location['name']); ?>
          </option>
        <?php endforeach; ?>
      </select>
    </div>
    <div class="col-md-2">
      <label class="form-label">Unit</label>
      <select name="unit_id" class="form-select">
        <option value="">All Units</option>
        <?php foreach ($units as $unit): ?>
          <option value="<?php echo (int)$unit['id']; ?>" <?php echo $unit_filter == $unit['id'] ? 'selected' : ''; ?>>
            <?php echo h($unit['name']); ?>
          </option>
        <?php endforeach; ?>
      </select>
    </div>
  </div>
  <div class="mt-3">
    <button type="submit" class="btn btn-primary">Filter</button>
    <a href="index.php" class="btn btn-outline-secondary">Clear</a>
  </div>
</form>

<div class="table-responsive bg-white rounded shadow-sm">
<table class="table table-sm table-striped align-middle mb-0">
  <thead class="table-light">
    <tr>
      <th>#</th>
      <th>Name</th>
      <th>Brand</th>
      <th>Model</th>
      <th>Condition</th>
      <th>Stock</th>
      <th>Unit</th>
      <th>Location</th>
      <th></th>
    </tr>
  </thead>
  <tbody>
    <?php if (empty($rows)): ?>
      <tr>
        <td colspan="8" class="text-center text-muted py-4">
          No items found. <a href="item_new.php">Add the first item</a>
        </td>
      </tr>
    <?php else: ?>
      <?php foreach ($rows as $r): ?>
        <tr>
          <td><?php echo (int)$r['id']; ?></td>
          <td><?php echo h($r['name']); ?></td>
          <td><?php echo h($r['brand']); ?></td>
          <td><?php echo h($r['model']); ?></td>
          <td><?php echo h($r['condition_status']); ?></td>
          <td><span class="badge bg-<?php echo ((int)$r['stock_qty']<=0?'danger':'primary'); ?>">
              <?php echo (int)($r['stock_qty'] ?? 0); ?></span></td>
          <td><?php echo h($r['unit_abbr'] ?: $r['unit']); ?></td>
          <td><?php echo h($r['location']); ?></td>
          <td class="text-nowrap">
            <a class="btn btn-sm btn-outline-primary" href="item_edit.php?id=<?php echo (int)$r['id']; ?>">Edit</a>
          </td>
        </tr>
      <?php endforeach; ?>
    <?php endif; ?>
  </tbody>
</table>
</div>

<div class="mt-3 d-flex justify-content-between align-items-center">
  <small class="text-muted">
    Showing <?php echo count($rows); ?> item(s)
    <?php if (!empty($search) || !empty($brand_filter) || !empty($condition_filter) || !empty($location_filter) || !empty($unit_filter)): ?>
      (filtered)
    <?php endif; ?>
  </small>

  <?php if (!empty($search) || !empty($brand_filter) || !empty($condition_filter) || !empty($location_filter) || !empty($unit_filter)): ?>
    <a href="index.php" class="btn btn-sm btn-outline-secondary">Clear All Filters</a>
  <?php endif; ?>
</div>

<?php include '_footer.php'; ?>