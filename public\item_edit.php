<?php include '_header.php'; $pdo = db();

$id = (int)($_GET['id'] ?? 0);

if ($_SERVER['REQUEST_METHOD']==='POST') {
    $stmt = $pdo->prepare("UPDATE items SET item_code=?, name=?, brand=?, model=?, condition_status=?, location=?, unit=?, unit_cost=?, notes=? WHERE id=?");
    $stmt->execute([
        $_POST['item_code'] ?? null,
        $_POST['name'] ?? '',
        $_POST['brand'] ?? null,
        $_POST['model'] ?? null,
        $_POST['condition_status'] ?? null,
        $_POST['location'] ?? null,
        $_POST['unit'] ?? 'pcs',
        $_POST['unit_cost'] ?? 0,
        $_POST['notes'] ?? null,
        $id
    ]);
    redirect('index.php');
}

$stmt = $pdo->prepare("SELECT * FROM items WHERE id=?");
$stmt->execute([$id]);
$item = $stmt->fetch();
if (!$item) { echo '<div class="alert alert-danger">Item not found.</div>'; include '_footer.php'; exit; }
?>
<h3>Edit Item #<?php echo (int)$item['id']; ?></h3>
<form method="post" class="bg-white p-3 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-3">
      <label class="form-label">Item Code</label>
      <input name="item_code" class="form-control" value="<?php echo h($item['item_code']); ?>">
    </div>
    <div class="col-md-6">
      <label class="form-label">Name *</label>
      <input name="name" required class="form-control" value="<?php echo h($item['name']); ?>">
    </div>
    <div class="col-md-3">
      <label class="form-label">Brand</label>
      <input name="brand" class="form-control" value="<?php echo h($item['brand']); ?>">
    </div>
    <div class="col-md-3">
      <label class="form-label">Model</label>
      <input name="model" class="form-control" value="<?php echo h($item['model']); ?>">
    </div>
    <div class="col-md-3">
      <label class="form-label">Condition</label>
      <input name="condition_status" class="form-control" value="<?php echo h($item['condition_status']); ?>">
    </div>
    <div class="col-md-3">
      <label class="form-label">Location</label>
      <input name="location" class="form-control" value="<?php echo h($item['location']); ?>">
    </div>
    <div class="col-md-2">
      <label class="form-label">Unit</label>
      <input name="unit" class="form-control" value="<?php echo h($item['unit']); ?>">
    </div>
    <div class="col-md-2">
      <label class="form-label">Unit Cost</label>
      <input name="unit_cost" type="number" step="0.01" class="form-control" value="<?php echo h($item['unit_cost']); ?>">
    </div>
    <div class="col-12">
      <label class="form-label">Notes</label>
      <textarea name="notes" class="form-control" rows="3"><?php echo h($item['notes']); ?></textarea>
    </div>
  </div>
  <div class="mt-3 d-flex justify-content-between">
    <div>
      <button class="btn btn-primary">Save</button>
      <a href="index.php" class="btn btn-secondary">Back</a>
    </div>
    <form method="post" action="item_remove.php" onsubmit="return confirm('Delete this item?');">
      <input type="hidden" name="id" value="<?php echo (int)$item['id']; ?>">
      <button class="btn btn-outline-danger">Delete</button>
    </form>
  </div>
</form>
<?php include '_footer.php'; ?>