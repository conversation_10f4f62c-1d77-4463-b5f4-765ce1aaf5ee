<?php include '_header.php'; $pdo = db();

if ($_SERVER['REQUEST_METHOD']==='POST') {
    $stmt = $pdo->prepare("INSERT INTO items (item_code,name,brand,model,condition_status,location,unit,unit_cost,notes)
                           VALUES (?,?,?,?,?,?,?,?,?)");
    $stmt->execute([
        $_POST['item_code'] ?? null,
        $_POST['name'] ?? '',
        $_POST['brand'] ?? null,
        $_POST['model'] ?? null,
        $_POST['condition_status'] ?? null,
        $_POST['location'] ?? null,
        $_POST['unit'] ?? 'pcs',
        $_POST['unit_cost'] ?? 0,
        $_POST['notes'] ?? null,
    ]);
    redirect('index.php');
}
?>
<h3>Add Item</h3>
<form method="post" class="bg-white p-3 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-3">
      <label class="form-label">Item Code</label>
      <input name="item_code" class="form-control">
    </div>
    <div class="col-md-6">
      <label class="form-label">Name *</label>
      <input name="name" required class="form-control">
    </div>
    <div class="col-md-3">
      <label class="form-label">Brand</label>
      <input name="brand" class="form-control">
    </div>
    <div class="col-md-3">
      <label class="form-label">Model</label>
      <input name="model" class="form-control">
    </div>
    <div class="col-md-3">
      <label class="form-label">Condition</label>
      <input name="condition_status" class="form-control" placeholder="New/Used/Good/etc">
    </div>
    <div class="col-md-3">
      <label class="form-label">Location</label>
      <input name="location" class="form-control" placeholder="Store/Office A...">
    </div>
    <div class="col-md-2">
      <label class="form-label">Unit</label>
      <input name="unit" class="form-control" value="pcs">
    </div>
    <div class="col-md-2">
      <label class="form-label">Unit Cost</label>
      <input name="unit_cost" type="number" step="0.01" class="form-control">
    </div>
    <div class="col-12">
      <label class="form-label">Notes</label>
      <textarea name="notes" class="form-control" rows="3"></textarea>
    </div>
  </div>
  <div class="mt-3">
    <button class="btn btn-primary">Save</button>
    <a href="index.php" class="btn btn-secondary">Cancel</a>
  </div>
</form>
<?php include '_footer.php'; ?>