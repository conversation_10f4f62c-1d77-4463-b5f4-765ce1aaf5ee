<?php include '_header.php'; $pdo = db();

// Get lookup data for dropdowns
$brands = $pdo->query("SELECT id, name FROM brands ORDER BY name ASC")->fetchAll();
$conditions = $pdo->query("SELECT id, name FROM conditions ORDER BY name ASC")->fetchAll();
$locations = $pdo->query("SELECT id, name FROM locations ORDER BY name ASC")->fetchAll();
$units = $pdo->query("SELECT id, name, abbreviation FROM units ORDER BY name ASC")->fetchAll();

if ($_SERVER['REQUEST_METHOD']==='POST') {
    $stmt = $pdo->prepare("INSERT INTO items (item_code,name,brand_id,model,condition_id,location_id,unit_id,unit_cost,notes)
                           VALUES (?,?,?,?,?,?,?,?,?)");
    $stmt->execute([
        $_POST['item_code'] ?? null,
        $_POST['name'] ?? '',
        $_POST['brand_id'] ?: null,
        $_POST['model'] ?? null,
        $_POST['condition_id'] ?: null,
        $_POST['location_id'] ?: null,
        $_POST['unit_id'] ?: null,
        $_POST['unit_cost'] ?? 0,
        $_POST['notes'] ?? null,
    ]);
    redirect('index.php');
}
?>
<h3>Add Item</h3>
<form method="post" class="bg-white p-3 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-3">
      <label class="form-label">Item Code</label>
      <input name="item_code" class="form-control">
    </div>
    <div class="col-md-6">
      <label class="form-label">Name *</label>
      <input name="name" required class="form-control">
    </div>
    <div class="col-md-3">
      <label class="form-label">Brand</label>
      <select name="brand_id" class="form-select">
        <option value="">Select Brand...</option>
        <?php foreach ($brands as $brand): ?>
          <option value="<?php echo (int)$brand['id']; ?>"><?php echo h($brand['name']); ?></option>
        <?php endforeach; ?>
      </select>
      <div class="form-text"><a href="brands.php" target="_blank">Manage brands</a></div>
    </div>
    <div class="col-md-3">
      <label class="form-label">Model</label>
      <input name="model" class="form-control">
    </div>
    <div class="col-md-3">
      <label class="form-label">Condition</label>
      <select name="condition_id" class="form-select">
        <option value="">Select Condition...</option>
        <?php foreach ($conditions as $condition): ?>
          <option value="<?php echo (int)$condition['id']; ?>"><?php echo h($condition['name']); ?></option>
        <?php endforeach; ?>
      </select>
      <div class="form-text"><a href="conditions.php" target="_blank">Manage conditions</a></div>
    </div>
    <div class="col-md-3">
      <label class="form-label">Location</label>
      <select name="location_id" class="form-select">
        <option value="">Select Location...</option>
        <?php foreach ($locations as $location): ?>
          <option value="<?php echo (int)$location['id']; ?>"><?php echo h($location['name']); ?></option>
        <?php endforeach; ?>
      </select>
      <div class="form-text"><a href="locations.php" target="_blank">Manage locations</a></div>
    </div>
    <div class="col-md-2">
      <label class="form-label">Unit</label>
      <select name="unit_id" class="form-select">
        <option value="">Select Unit...</option>
        <?php foreach ($units as $unit): ?>
          <option value="<?php echo (int)$unit['id']; ?>" <?php echo $unit['name'] === 'Pieces' ? 'selected' : ''; ?>>
            <?php echo h($unit['name']); ?><?php echo $unit['abbreviation'] ? ' (' . h($unit['abbreviation']) . ')' : ''; ?>
          </option>
        <?php endforeach; ?>
      </select>
      <div class="form-text"><a href="units.php" target="_blank">Manage units</a></div>
    </div>
    <div class="col-md-2">
      <label class="form-label">Unit Cost</label>
      <input name="unit_cost" type="number" step="0.01" class="form-control">
    </div>
    <div class="col-12">
      <label class="form-label">Notes</label>
      <textarea name="notes" class="form-control" rows="3"></textarea>
    </div>
  </div>
  <div class="mt-3">
    <button class="btn btn-primary">Save</button>
    <a href="index.php" class="btn btn-secondary">Cancel</a>
  </div>
</form>
<?php include '_footer.php'; ?>