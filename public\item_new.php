<?php include '_header.php'; $pdo = db();

// Get lookup data for dropdowns
$categories = $pdo->query("SELECT id, name FROM categories ORDER BY name ASC")->fetchAll();
$brands = $pdo->query("SELECT id, name FROM brands ORDER BY name ASC")->fetchAll();
$conditions = $pdo->query("SELECT id, name FROM conditions ORDER BY name ASC")->fetchAll();
$locations = $pdo->query("SELECT id, name FROM locations ORDER BY name ASC")->fetchAll();
$units = $pdo->query("SELECT id, name, abbreviation FROM units ORDER BY name ASC")->fetchAll();

if ($_SERVER['REQUEST_METHOD']==='POST') {
    $stmt = $pdo->prepare("INSERT INTO items (item_code,name,category_id,brand_id,model,condition_id,location_id,unit_id,unit_cost,serial_number,asset_tag,warranty_date,notes)
                           VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)");
    $stmt->execute([
        $_POST['item_code'] ?? null,
        $_POST['name'] ?? '',
        $_POST['category_id'] ?: null,
        $_POST['brand_id'] ?: null,
        $_POST['model'] ?? null,
        $_POST['condition_id'] ?: null,
        $_POST['location_id'] ?: null,
        $_POST['unit_id'] ?: null,
        $_POST['unit_cost'] ?? 0,
        $_POST['serial_number'] ?? null,
        $_POST['asset_tag'] ?? null,
        $_POST['warranty_date'] ?: null,
        $_POST['notes'] ?? null,
    ]);
    redirect('index.php');
}
?>
<h3>Add Equipment</h3>
<form method="post" class="bg-white p-3 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-3">
      <label class="form-label">Item Code</label>
      <input name="item_code" class="form-control" placeholder="e.g., IT-001">
    </div>
    <div class="col-md-6">
      <label class="form-label">Equipment Name *</label>
      <input name="name" required class="form-control" placeholder="e.g., Dell OptiPlex 7090">
    </div>
    <div class="col-md-3">
      <label class="form-label">Category</label>
      <select name="category_id" class="form-select">
        <option value="">Select Category...</option>
        <?php foreach ($categories as $category): ?>
          <option value="<?php echo (int)$category['id']; ?>"><?php echo h($category['name']); ?></option>
        <?php endforeach; ?>
      </select>
      <div class="form-text"><a href="categories.php" target="_blank">Manage categories</a></div>
    </div>
    <div class="col-md-3">
      <label class="form-label">Brand</label>
      <select name="brand_id" class="form-select">
        <option value="">Select Brand...</option>
        <?php foreach ($brands as $brand): ?>
          <option value="<?php echo (int)$brand['id']; ?>"><?php echo h($brand['name']); ?></option>
        <?php endforeach; ?>
      </select>
      <div class="form-text"><a href="brands.php" target="_blank">Manage brands</a></div>
    </div>
    <div class="col-md-3">
      <label class="form-label">Model</label>
      <input name="model" class="form-control">
    </div>
    <div class="col-md-3">
      <label class="form-label">Condition</label>
      <select name="condition_id" class="form-select">
        <option value="">Select Condition...</option>
        <?php foreach ($conditions as $condition): ?>
          <option value="<?php echo (int)$condition['id']; ?>"><?php echo h($condition['name']); ?></option>
        <?php endforeach; ?>
      </select>
      <div class="form-text"><a href="conditions.php" target="_blank">Manage conditions</a></div>
    </div>
    <div class="col-md-3">
      <label class="form-label">Location</label>
      <select name="location_id" class="form-select">
        <option value="">Select Location...</option>
        <?php foreach ($locations as $location): ?>
          <option value="<?php echo (int)$location['id']; ?>"><?php echo h($location['name']); ?></option>
        <?php endforeach; ?>
      </select>
      <div class="form-text"><a href="locations.php" target="_blank">Manage locations</a></div>
    </div>
    <div class="col-md-2">
      <label class="form-label">Unit</label>
      <select name="unit_id" class="form-select">
        <option value="">Select Unit...</option>
        <?php foreach ($units as $unit): ?>
          <option value="<?php echo (int)$unit['id']; ?>" <?php echo $unit['name'] === 'Pieces' ? 'selected' : ''; ?>>
            <?php echo h($unit['name']); ?><?php echo $unit['abbreviation'] ? ' (' . h($unit['abbreviation']) . ')' : ''; ?>
          </option>
        <?php endforeach; ?>
      </select>
      <div class="form-text"><a href="units.php" target="_blank">Manage units</a></div>
    </div>
    <div class="col-md-2">
      <label class="form-label">Unit Cost</label>
      <input name="unit_cost" type="number" step="0.01" class="form-control" placeholder="0.00">
    </div>
    <div class="col-md-4">
      <label class="form-label">Serial Number</label>
      <input name="serial_number" class="form-control" placeholder="Device serial number">
    </div>
    <div class="col-md-3">
      <label class="form-label">Asset Tag</label>
      <input name="asset_tag" class="form-control" placeholder="Company asset tag">
    </div>
    <div class="col-md-3">
      <label class="form-label">Warranty Date</label>
      <input name="warranty_date" type="date" class="form-control">
    </div>
    <div class="col-12">
      <label class="form-label">Notes</label>
      <textarea name="notes" class="form-control" rows="3" placeholder="Additional notes about this equipment..."></textarea>
    </div>
  </div>
  <div class="mt-3">
    <button class="btn btn-primary">Save</button>
    <a href="index.php" class="btn btn-secondary">Cancel</a>
  </div>
</form>
<?php include '_footer.php'; ?>