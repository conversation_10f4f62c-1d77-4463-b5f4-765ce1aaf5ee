<?php require_once __DIR__.'/../db.php'; require_once __DIR__.'/../lib/helpers.php'; 

$Base_URL= '/inventory-php-mysql-app/public/';

?>
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Inventory</title>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
  <div class="container-fluid">
    <a class="navbar-brand" href="<?php echo base_url($Base_URL.'index.php'); ?>">Inventory</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <div class="navbar-nav me-auto">
        <a class="nav-link" href="<?php echo base_url($Base_URL.'index.php'); ?>">Items</a>
        <a class="nav-link" href="<?php echo base_url($Base_URL.'item_new.php'); ?>">Add Item</a>
        <a class="nav-link" href="<?php echo base_url($Base_URL.'txn_new.php'); ?>">New Transaction</a>
        <a class="nav-link" href="<?php echo base_url($Base_URL.'transactions.php'); ?>">Transactions</a>
      </div>
      <div class="navbar-nav">
        <div class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
            Manage
          </a>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="<?php echo base_url($Base_URL.'brands.php'); ?>">Brands</a></li>
            <li><a class="dropdown-item" href="<?php echo base_url($Base_URL.'conditions.php'); ?>">Conditions</a></li>
            <li><a class="dropdown-item" href="<?php echo base_url($Base_URL.'locations.php'); ?>">Locations</a></li>
            <li><a class="dropdown-item" href="<?php echo base_url($Base_URL.'units.php'); ?>">Units</a></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</nav>
<div class="container py-4">
