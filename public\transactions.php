<?php include '_header.php'; $pdo = db();

$rows = $pdo->query("SELECT t.*, i.name AS item_name
                     FROM inventory_transactions t
                     JOIN items i ON i.id=t.item_id
                     ORDER BY t.tx_date DESC, t.id DESC
                     LIMIT 500")->fetchAll();
?>
<h3>Transactions</h3>
<div class="table-responsive bg-white rounded shadow-sm">
<table class="table table-sm table-striped align-middle mb-0">
  <thead class="table-light">
    <tr>
      <th>Date</th>
      <th>Item</th>
      <th>Type</th>
      <th>Qty</th>
      <th>Reference</th>
      <th>Notes</th>
    </tr>
  </thead>
  <tbody>
    <?php foreach ($rows as $r): ?>
      <tr>
        <td><?php echo h($r['tx_date']); ?></td>
        <td><?php echo h($r['item_name']); ?></td>
        <td><?php echo strtoupper(h($r['tx_type'])); ?></td>
        <td><?php echo (int)$r['qty']; ?></td>
        <td><?php echo h($r['reference']); ?></td>
        <td><?php echo h($r['notes']); ?></td>
      </tr>
    <?php endforeach; ?>
  </tbody>
</table>
</div>
<?php include '_footer.php'; ?>