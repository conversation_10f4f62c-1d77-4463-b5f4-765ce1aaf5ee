<?php include '_header.php'; 

$pdo = db();
$id = (int)($_GET['id'] ?? 0);
$error = '';
$success = '';

// Get the category
$stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
$stmt->execute([$id]);
$category = $stmt->fetch();

if (!$category) {
    echo '<div class="alert alert-danger">Category not found.</div>';
    include '_footer.php';
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (empty($name)) {
        $error = 'Category name is required.';
    } else {
        try {
            $stmt = $pdo->prepare("UPDATE categories SET name = ?, description = ? WHERE id = ?");
            $stmt->execute([$name, $description, $id]);
            $success = 'Category updated successfully!';
            // Refresh category data
            $category['name'] = $name;
            $category['description'] = $description;
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry
                $error = 'A category with this name already exists.';
            } else {
                $error = 'An error occurred while updating the category.';
            }
        }
    }
}

// Check if category is used by any items
$stmt = $pdo->prepare("SELECT COUNT(*) as item_count FROM items WHERE category_id = ?");
$stmt->execute([$id]);
$usage = $stmt->fetch();
$itemCount = (int)$usage['item_count'];
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Edit Category #<?php echo (int)$category['id']; ?></h3>
  <a href="categories.php" class="btn btn-secondary">Back to Categories</a>
</div>

<?php if ($error): ?>
  <div class="alert alert-danger"><?php echo h($error); ?></div>
<?php endif; ?>

<?php if ($success): ?>
  <div class="alert alert-success"><?php echo h($success); ?></div>
<?php endif; ?>

<form method="post" class="bg-white p-4 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-6">
      <label class="form-label">Category Name *</label>
      <input name="name" required class="form-control" value="<?php echo h($category['name']); ?>" placeholder="e.g., Computers, Networking, Printers">
      <div class="form-text">Enter a unique category name</div>
    </div>
    <div class="col-12">
      <label class="form-label">Description</label>
      <textarea name="description" class="form-control" rows="3" placeholder="Describe what types of equipment belong in this category"><?php echo h($category['description']); ?></textarea>
      <div class="form-text">Optional description to help identify what equipment belongs in this category</div>
    </div>
  </div>
  
  <div class="mt-4 d-flex justify-content-between">
    <div>
      <button type="submit" class="btn btn-primary">Update Category</button>
      <a href="categories.php" class="btn btn-secondary">Cancel</a>
    </div>
    
    <?php if ($itemCount > 0): ?>
      <div class="text-muted small">
        <i class="fas fa-info-circle"></i> This category is used by <?php echo $itemCount; ?> equipment item(s)
      </div>
    <?php else: ?>
      <form method="post" action="category_delete.php" onsubmit="return confirm('Delete this category? This action cannot be undone.');">
        <input type="hidden" name="id" value="<?php echo (int)$category['id']; ?>">
        <button class="btn btn-outline-danger">Delete Category</button>
      </form>
    <?php endif; ?>
  </div>
</form>

<div class="mt-4">
  <div class="card">
    <div class="card-body">
      <h6 class="card-title">Category Information</h6>
      <div class="row">
        <div class="col-md-6">
          <small class="text-muted">Created:</small><br>
          <strong><?php echo date('M j, Y g:i A', strtotime($category['created_at'])); ?></strong>
        </div>
        <div class="col-md-6">
          <small class="text-muted">Last Updated:</small><br>
          <strong><?php echo date('M j, Y g:i A', strtotime($category['updated_at'])); ?></strong>
        </div>
      </div>
      <?php if ($itemCount > 0): ?>
        <div class="mt-2">
          <small class="text-muted">Equipment using this category:</small><br>
          <strong><?php echo $itemCount; ?> item(s)</strong>
          <a href="index.php?category_id=<?php echo $id; ?>" class="btn btn-sm btn-outline-primary ms-2">View Equipment</a>
        </div>
      <?php endif; ?>
    </div>
  </div>
</div>

<?php include '_footer.php'; ?>
