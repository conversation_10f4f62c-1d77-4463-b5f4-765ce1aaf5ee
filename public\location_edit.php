<?php include '_header.php'; 

$pdo = db();
$id = (int)($_GET['id'] ?? 0);
$error = '';
$success = '';

// Get the location
$stmt = $pdo->prepare("SELECT * FROM locations WHERE id = ?");
$stmt->execute([$id]);
$location = $stmt->fetch();

if (!$location) {
    echo '<div class="alert alert-danger">Location not found.</div>';
    include '_footer.php';
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (empty($name)) {
        $error = 'Location name is required.';
    } else {
        try {
            $stmt = $pdo->prepare("UPDATE locations SET name = ?, description = ? WHERE id = ?");
            $stmt->execute([$name, $description, $id]);
            $success = 'Location updated successfully!';
            // Refresh location data
            $location['name'] = $name;
            $location['description'] = $description;
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry
                $error = 'A location with this name already exists.';
            } else {
                $error = 'An error occurred while updating the location.';
            }
        }
    }
}

// Check if location is used by any items
$stmt = $pdo->prepare("SELECT COUNT(*) as item_count FROM items WHERE location_id = ?");
$stmt->execute([$id]);
$usage = $stmt->fetch();
$itemCount = (int)$usage['item_count'];
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Edit Location #<?php echo (int)$location['id']; ?></h3>
  <a href="locations.php" class="btn btn-secondary">Back to Locations</a>
</div>

<?php if ($error): ?>
  <div class="alert alert-danger"><?php echo h($error); ?></div>
<?php endif; ?>

<?php if ($success): ?>
  <div class="alert alert-success"><?php echo h($success); ?></div>
<?php endif; ?>

<form method="post" class="bg-white p-4 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-6">
      <label class="form-label">Location Name *</label>
      <input name="name" required class="form-control" value="<?php echo h($location['name']); ?>" placeholder="e.g., Main Warehouse, Office A, Store Front">
      <div class="form-text">Enter a unique location name</div>
    </div>
    <div class="col-12">
      <label class="form-label">Description</label>
      <textarea name="description" class="form-control" rows="3" placeholder="Optional description about this location"><?php echo h($location['description']); ?></textarea>
      <div class="form-text">Optional description or notes about this location</div>
    </div>
  </div>
  
  <div class="mt-4 d-flex justify-content-between">
    <div>
      <button type="submit" class="btn btn-primary">Update Location</button>
      <a href="locations.php" class="btn btn-secondary">Cancel</a>
    </div>
    
    <?php if ($itemCount > 0): ?>
      <div class="text-muted small">
        <i class="fas fa-info-circle"></i> This location is used by <?php echo $itemCount; ?> item(s)
      </div>
    <?php else: ?>
      <form method="post" action="location_delete.php" onsubmit="return confirm('Delete this location? This action cannot be undone.');">
        <input type="hidden" name="id" value="<?php echo (int)$location['id']; ?>">
        <button class="btn btn-outline-danger">Delete Location</button>
      </form>
    <?php endif; ?>
  </div>
</form>

<?php include '_footer.php'; ?>
