<?php include '_header.php';

$pdo = db();
$search = $_GET['q'] ?? '';
$sql = "SELECT * FROM conditions WHERE name LIKE :q ORDER BY name ASC";
$stmt = $pdo->prepare($sql);
$stmt->execute([':q' => '%' . $search . '%']);
$conditions = $stmt->fetchAll();
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Conditions</h3>
  <div class="d-flex gap-2">
    <form class="d-flex" method="get">
      <input class="form-control me-2" type="search" name="q" placeholder="Search conditions..." value="<?php echo h($search); ?>">
      <button class="btn btn-outline-secondary" type="submit">Search</button>
    </form>
    <a href="condition_new.php" class="btn btn-primary">Add Condition</a>
  </div>
</div>

<div class="table-responsive bg-white rounded shadow-sm">
<table class="table table-sm table-striped align-middle mb-0">
  <thead class="table-light">
    <tr>
      <th>#</th>
      <th>Name</th>
      <th>Description</th>
      <th>Created</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    <?php if (empty($conditions)): ?>
      <tr>
        <td colspan="5" class="text-center text-muted py-4">
          <?php if ($search): ?>
            No conditions found matching "<?php echo h($search); ?>"
          <?php else: ?>
            No conditions found. <a href="condition_new.php">Add the first condition</a>
          <?php endif; ?>
        </td>
      </tr>
    <?php else: ?>
      <?php foreach ($conditions as $condition): ?>
        <tr>
          <td><?php echo (int)$condition['id']; ?></td>
          <td><strong><?php echo h($condition['name']); ?></strong></td>
          <td><?php echo h($condition['description']); ?></td>
          <td><?php echo date('M j, Y', strtotime($condition['created_at'])); ?></td>
          <td class="text-nowrap">
            <a class="btn btn-sm btn-outline-primary" href="condition_edit.php?id=<?php echo (int)$condition['id']; ?>">Edit</a>
            <form method="post" action="condition_delete.php" class="d-inline" onsubmit="return confirm('Delete this condition? This action cannot be undone.');">
              <input type="hidden" name="id" value="<?php echo (int)$condition['id']; ?>">
              <button class="btn btn-sm btn-outline-danger">Delete</button>
            </form>
          </td>
        </tr>
      <?php endforeach; ?>
    <?php endif; ?>
  </tbody>
</table>
</div>

<div class="mt-3">
  <small class="text-muted">
    Total: <?php echo count($conditions); ?> condition(s)
  </small>
</div>

<?php include '_footer.php'; ?>
