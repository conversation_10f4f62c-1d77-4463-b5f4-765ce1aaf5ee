<?php include '_header.php'; 

$pdo = db();
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (empty($name)) {
        $error = 'Brand name is required.';
    } else {
        try {
            $stmt = $pdo->prepare("INSERT INTO brands (name, description) VALUES (?, ?)");
            $stmt->execute([$name, $description]);
            $success = 'Brand added successfully!';
            // Clear form
            $name = '';
            $description = '';
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry
                $error = 'A brand with this name already exists.';
            } else {
                $error = 'An error occurred while saving the brand.';
            }
        }
    }
}
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Add Brand</h3>
  <a href="brands.php" class="btn btn-secondary">Back to Brands</a>
</div>

<?php if ($error): ?>
  <div class="alert alert-danger"><?php echo h($error); ?></div>
<?php endif; ?>

<?php if ($success): ?>
  <div class="alert alert-success"><?php echo h($success); ?></div>
<?php endif; ?>

<form method="post" class="bg-white p-4 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-6">
      <label class="form-label">Brand Name *</label>
      <input name="name" required class="form-control" value="<?php echo h($name ?? ''); ?>" placeholder="e.g., Apple, Samsung, Dell">
      <div class="form-text">Enter a unique brand name</div>
    </div>
    <div class="col-12">
      <label class="form-label">Description</label>
      <textarea name="description" class="form-control" rows="3" placeholder="Optional description about this brand"><?php echo h($description ?? ''); ?></textarea>
      <div class="form-text">Optional description or notes about this brand</div>
    </div>
  </div>
  
  <div class="mt-4">
    <button type="submit" class="btn btn-primary">Save Brand</button>
    <a href="brands.php" class="btn btn-secondary">Cancel</a>
  </div>
</form>

<div class="mt-4">
  <div class="card">
    <div class="card-body">
      <h6 class="card-title">Tips</h6>
      <ul class="mb-0 small text-muted">
        <li>Brand names must be unique</li>
        <li>Use consistent naming (e.g., "Apple" not "apple" or "APPLE")</li>
        <li>Add a description to help identify the brand later</li>
      </ul>
    </div>
  </div>
</div>

<?php include '_footer.php'; ?>
