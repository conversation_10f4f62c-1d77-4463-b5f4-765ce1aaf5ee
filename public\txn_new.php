<?php include '_header.php'; $pdo = db();

if ($_SERVER['REQUEST_METHOD']==='POST') {
    $stmt = $pdo->prepare("INSERT INTO inventory_transactions (item_id, tx_type, qty, reference, tx_date, notes)
                           VALUES (?,?,?,?,?,?)");
    $stmt->execute([
        (int)$_POST['item_id'],
        $_POST['tx_type'],
        max(0, (int)$_POST['qty']),
        $_POST['reference'] ?? null,
        $_POST['tx_date'] ?? date('Y-m-d'),
        $_POST['notes'] ?? null,
    ]);
    redirect('index.php');
}

$items = $pdo->query("SELECT id, name FROM items ORDER BY name ASC")->fetchAll();
?>
<h3>New Transaction</h3>
<form method="post" class="bg-white p-3 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-6">
      <label class="form-label">Item</label>
      <select name="item_id" class="form-select" required>
        <option value="">-- select --</option>
        <?php foreach ($items as $i): ?>
          <option value="<?php echo (int)$i['id']; ?>"><?php echo h($i['name']); ?></option>
        <?php endforeach; ?>
      </select>
    </div>
    <div class="col-md-3">
      <label class="form-label">Type</label>
      <select name="tx_type" class="form-select" required>
        <option value="in">IN (Receive)</option>
        <option value="out">OUT (Issue)</option>
        <option value="adjust">ADJUST (+/-)</option>
      </select>
    </div>
    <div class="col-md-3">
      <label class="form-label">Quantity</label>
      <input name="qty" type="number" min="0" class="form-control" required>
    </div>
    <div class="col-md-4">
      <label class="form-label">Date</label>
      <input name="tx_date" type="date" class="form-control" value="<?php echo date('Y-m-d'); ?>">
    </div>
    <div class="col-md-4">
      <label class="form-label">Reference</label>
      <input name="reference" class="form-control" placeholder="PO#, Ticket#...">
    </div>
    <div class="col-md-12">
      <label class="form-label">Notes</label>
      <textarea name="notes" class="form-control" rows="2"></textarea>
    </div>
  </div>
  <div class="mt-3">
    <button class="btn btn-primary">Save</button>
    <a href="index.php" class="btn btn-secondary">Cancel</a>
  </div>
</form>
<?php include '_footer.php'; ?>