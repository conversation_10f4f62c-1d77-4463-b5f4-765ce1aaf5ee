
-- Create database (run once, or create manually)
-- CREATE DATABASE inventory_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE inventory_db;

CREATE TABLE IF NOT EXISTS items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  item_code VARCHAR(64) NULL,
  name VARCHAR(255) NOT NULL,
  brand VARCHAR(120) NULL,
  model VARCHAR(120) NULL,
  condition_status VARCHAR(80) NULL,
  location VARCHAR(160) NULL,
  unit VARCHAR(20) DEFAULT 'pcs',
  unit_cost DECIMAL(12,2) DEFAULT 0,
  notes TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS inventory_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  item_id INT NOT NULL,
  tx_type ENUM('in','out','adjust') NOT NULL,
  qty INT NOT NULL,
  reference VARCHAR(120) NULL,
  tx_date DATE NOT NULL,
  notes TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- Helper view for stock balance per item
CREATE OR REPLACE VIEW v_item_stock AS
SELECT
  i.id as item_id,
  i.name,
  i.brand,
  i.model,
  COALESCE(SUM(CASE WHEN t.tx_type='in' THEN t.qty WHEN t.tx_type='adjust' THEN t.qty ELSE 0 END),0)
  - COALESCE(SUM(CASE WHEN t.tx_type='out' THEN t.qty ELSE 0 END),0) AS stock_qty
FROM items i
LEFT JOIN inventory_transactions t ON t.item_id = i.id
GROUP BY i.id, i.name, i.brand, i.model;

-- Example indexes
CREATE INDEX idx_tx_item ON inventory_transactions(item_id);
CREATE INDEX idx_tx_date ON inventory_transactions(tx_date);
