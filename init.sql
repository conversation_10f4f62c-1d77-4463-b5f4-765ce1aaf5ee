
-- Create database (run once, or create manually)
-- CREATE DATABASE inventory_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE inventory_db;

-- Lookup tables for brands, conditions, locations, and units
CREATE TABLE IF NOT EXISTS brands (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(120) NOT NULL UNIQUE,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS conditions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(80) NOT NULL UNIQUE,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS locations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(160) NOT NULL UNIQUE,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS units (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(20) NOT NULL UNIQUE,
  abbreviation VARCHAR(10) NULL,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Insert default data for lookup tables
INSERT IGNORE INTO brands (name, description) VALUES
('Generic', 'Generic or unbranded items'),
('Apple', 'Apple Inc. products'),
('Samsung', 'Samsung Electronics'),
('Dell', 'Dell Technologies'),
('HP', 'Hewlett-Packard'),
('Lenovo', 'Lenovo Group');

INSERT IGNORE INTO conditions (name, description) VALUES
('New', 'Brand new, unused condition'),
('Like New', 'Excellent condition, minimal use'),
('Good', 'Good working condition with minor wear'),
('Fair', 'Working condition with noticeable wear'),
('Poor', 'Functional but significant wear'),
('Refurbished', 'Professionally restored to working condition'),
('Damaged', 'Not working or significantly damaged');

INSERT IGNORE INTO locations (name, description) VALUES
('Main Warehouse', 'Primary storage facility'),
('Office A', 'Main office location'),
('Office B', 'Secondary office location'),
('Store Front', 'Retail store location'),
('Storage Room', 'General storage area'),
('IT Department', 'Information Technology department');

INSERT IGNORE INTO units (name, abbreviation, description) VALUES
('Pieces', 'pcs', 'Individual items or pieces'),
('Boxes', 'box', 'Items sold by the box'),
('Kilograms', 'kg', 'Weight measurement in kilograms'),
('Liters', 'L', 'Volume measurement in liters'),
('Meters', 'm', 'Length measurement in meters'),
('Sets', 'set', 'Items sold as complete sets'),
('Pairs', 'pair', 'Items sold in pairs');

CREATE TABLE IF NOT EXISTS items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  item_code VARCHAR(64) NULL,
  name VARCHAR(255) NOT NULL,
  brand_id INT NULL,
  model VARCHAR(120) NULL,
  condition_id INT NULL,
  location_id INT NULL,
  unit_id INT NULL,
  unit_cost DECIMAL(12,2) DEFAULT 0,
  notes TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE SET NULL,
  FOREIGN KEY (condition_id) REFERENCES conditions(id) ON DELETE SET NULL,
  FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE SET NULL,
  FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE SET NULL
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS inventory_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  item_id INT NOT NULL,
  tx_type ENUM('in','out','adjust') NOT NULL,
  qty INT NOT NULL,
  reference VARCHAR(120) NULL,
  tx_date DATE NOT NULL,
  notes TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- Helper view for stock balance per item with lookup data
CREATE OR REPLACE VIEW v_item_stock AS
SELECT
  i.id as item_id,
  i.name,
  b.name as brand,
  i.model,
  c.name as condition_status,
  l.name as location,
  u.name as unit,
  u.abbreviation as unit_abbr,
  COALESCE(SUM(CASE WHEN t.tx_type='in' THEN t.qty WHEN t.tx_type='adjust' THEN t.qty ELSE 0 END),0)
  - COALESCE(SUM(CASE WHEN t.tx_type='out' THEN t.qty ELSE 0 END),0) AS stock_qty
FROM items i
LEFT JOIN brands b ON i.brand_id = b.id
LEFT JOIN conditions c ON i.condition_id = c.id
LEFT JOIN locations l ON i.location_id = l.id
LEFT JOIN units u ON i.unit_id = u.id
LEFT JOIN inventory_transactions t ON t.item_id = i.id
GROUP BY i.id, i.name, b.name, i.model, c.name, l.name, u.name, u.abbreviation;

-- Example indexes
CREATE INDEX idx_tx_item ON inventory_transactions(item_id);
CREATE INDEX idx_tx_date ON inventory_transactions(tx_date);
