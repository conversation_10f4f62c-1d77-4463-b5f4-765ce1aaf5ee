
-- Create database (run once, or create manually)
-- CREATE DATABASE inventory_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE inventory_db;

-- Lookup tables for brands, conditions, locations, units, and categories
CREATE TABLE IF NOT EXISTS categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS brands (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(120) NOT NULL UNIQUE,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS conditions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(80) NOT NULL UNIQUE,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS locations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(160) NOT NULL UNIQUE,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS units (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(20) NOT NULL UNIQUE,
  abbreviation VARCHAR(10) NULL,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Insert default data for lookup tables
INSERT IGNORE INTO categories (name, description) VALUES
('Computers', 'Desktop computers, laptops, workstations'),
('Monitors', 'Computer monitors and displays'),
('Peripherals', 'Keyboards, mice, speakers, webcams'),
('Networking', 'Routers, switches, access points, cables'),
('Printers', 'Printers, scanners, multifunction devices'),
('Storage', 'Hard drives, SSDs, USB drives, NAS devices'),
('Servers', 'Server hardware and rack equipment'),
('Mobile Devices', 'Tablets, smartphones, mobile accessories'),
('Audio/Video', 'Projectors, cameras, microphones, headsets'),
('Cables & Adapters', 'Various cables, adapters, and connectors'),
('Power & UPS', 'Power supplies, UPS units, power strips'),
('Tools & Accessories', 'IT tools, cleaning supplies, spare parts');

INSERT IGNORE INTO brands (name, description) VALUES
('Generic', 'Generic or unbranded items'),
-- Computer Manufacturers
('Apple', 'Apple Inc. computers and devices'),
('Dell', 'Dell Technologies computers and servers'),
('HP', 'Hewlett-Packard computers and printers'),
('Lenovo', 'Lenovo computers and ThinkPad series'),
('ASUS', 'ASUS computers and components'),
('Acer', 'Acer computers and monitors'),
('MSI', 'MSI computers and gaming hardware'),
-- Networking Equipment
('Cisco', 'Cisco networking equipment'),
('Netgear', 'Netgear routers and switches'),
('TP-Link', 'TP-Link networking devices'),
('Ubiquiti', 'Ubiquiti enterprise networking'),
('D-Link', 'D-Link networking equipment'),
('Linksys', 'Linksys routers and networking'),
-- Peripherals & Accessories
('Logitech', 'Logitech mice, keyboards, webcams'),
('Microsoft', 'Microsoft keyboards, mice, Surface devices'),
('Razer', 'Razer gaming peripherals'),
('Corsair', 'Corsair gaming and PC components'),
('SteelSeries', 'SteelSeries gaming accessories'),
-- Monitors & Displays
('Samsung', 'Samsung monitors and displays'),
('LG', 'LG monitors and displays'),
('BenQ', 'BenQ monitors and projectors'),
('ViewSonic', 'ViewSonic monitors and displays'),
-- Printers
('Canon', 'Canon printers and scanners'),
('Epson', 'Epson printers and projectors'),
('Brother', 'Brother printers and label makers'),
('Xerox', 'Xerox printers and copiers'),
-- Storage & Components
('Western Digital', 'WD hard drives and storage'),
('Seagate', 'Seagate hard drives and storage'),
('Samsung Storage', 'Samsung SSDs and memory'),
('Kingston', 'Kingston memory and storage'),
('SanDisk', 'SanDisk flash storage and cards'),
-- Audio/Video
('Sony', 'Sony cameras and audio equipment'),
('Panasonic', 'Panasonic cameras and projectors'),
('JBL', 'JBL speakers and audio equipment'),
('Bose', 'Bose audio equipment'),
-- Power & UPS
('APC', 'APC UPS and power protection'),
('CyberPower', 'CyberPower UPS systems'),
('Tripp Lite', 'Tripp Lite power and connectivity');

INSERT IGNORE INTO conditions (name, description) VALUES
('New', 'Brand new, unused condition'),
('Like New', 'Excellent condition, minimal use'),
('Good', 'Good working condition with minor wear'),
('Fair', 'Working condition with noticeable wear'),
('Poor', 'Functional but significant wear'),
('Refurbished', 'Professionally restored to working condition'),
('Damaged', 'Not working or significantly damaged');

INSERT IGNORE INTO locations (name, description) VALUES
('IT Storage Room', 'Main IT equipment storage'),
('Server Room', 'Data center and server equipment'),
('IT Department', 'IT staff workspace and equipment'),
('Office Floor 1', 'First floor office workstations'),
('Office Floor 2', 'Second floor office workstations'),
('Conference Rooms', 'Meeting rooms and presentation equipment'),
('Reception Area', 'Front desk and visitor area'),
('Warehouse', 'General equipment warehouse'),
('Repair Workshop', 'Equipment repair and testing area'),
('Deployment Staging', 'Equipment preparation for deployment'),
('Remote Locations', 'Off-site or remote office equipment'),
('Retired/Disposal', 'Equipment pending disposal or recycling');

INSERT IGNORE INTO units (name, abbreviation, description) VALUES
('Pieces', 'pcs', 'Individual items or pieces'),
('Boxes', 'box', 'Items sold by the box'),
('Kilograms', 'kg', 'Weight measurement in kilograms'),
('Liters', 'L', 'Volume measurement in liters'),
('Meters', 'm', 'Length measurement in meters'),
('Sets', 'set', 'Items sold as complete sets'),
('Pairs', 'pair', 'Items sold in pairs');

CREATE TABLE IF NOT EXISTS items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  item_code VARCHAR(64) NULL,
  name VARCHAR(255) NOT NULL,
  category_id INT NULL,
  brand_id INT NULL,
  model VARCHAR(120) NULL,
  condition_id INT NULL,
  location_id INT NULL,
  unit_id INT NULL,
  unit_cost DECIMAL(12,2) DEFAULT 0,
  serial_number VARCHAR(100) NULL,
  asset_tag VARCHAR(50) NULL,
  warranty_date DATE NULL,
  notes TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
  FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE SET NULL,
  FOREIGN KEY (condition_id) REFERENCES conditions(id) ON DELETE SET NULL,
  FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE SET NULL,
  FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE SET NULL
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS inventory_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  item_id INT NOT NULL,
  tx_type ENUM('in','out','adjust') NOT NULL,
  qty INT NOT NULL,
  reference VARCHAR(120) NULL,
  tx_date DATE NOT NULL,
  notes TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- Helper view for stock balance per item with lookup data
CREATE OR REPLACE VIEW v_item_stock AS
SELECT
  i.id as item_id,
  i.name,
  cat.name as category,
  b.name as brand,
  i.model,
  c.name as condition_status,
  l.name as location,
  u.name as unit,
  u.abbreviation as unit_abbr,
  i.serial_number,
  i.asset_tag,
  i.warranty_date,
  COALESCE(SUM(CASE WHEN t.tx_type='in' THEN t.qty WHEN t.tx_type='adjust' THEN t.qty ELSE 0 END),0)
  - COALESCE(SUM(CASE WHEN t.tx_type='out' THEN t.qty ELSE 0 END),0) AS stock_qty
FROM items i
LEFT JOIN categories cat ON i.category_id = cat.id
LEFT JOIN brands b ON i.brand_id = b.id
LEFT JOIN conditions c ON i.condition_id = c.id
LEFT JOIN locations l ON i.location_id = l.id
LEFT JOIN units u ON i.unit_id = u.id
LEFT JOIN inventory_transactions t ON t.item_id = i.id
GROUP BY i.id, i.name, cat.name, b.name, i.model, c.name, l.name, u.name, u.abbreviation, i.serial_number, i.asset_tag, i.warranty_date;

-- Example indexes
CREATE INDEX idx_tx_item ON inventory_transactions(item_id);
CREATE INDEX idx_tx_date ON inventory_transactions(tx_date);
