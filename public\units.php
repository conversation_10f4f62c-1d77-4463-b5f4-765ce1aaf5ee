<?php include '_header.php';

$pdo = db();
$search = $_GET['q'] ?? '';
$sql = "SELECT * FROM units WHERE name LIKE :q OR abbreviation LIKE :q ORDER BY name ASC";
$stmt = $pdo->prepare($sql);
$stmt->execute([':q' => '%' . $search . '%']);
$units = $stmt->fetchAll();
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Units</h3>
  <div class="d-flex gap-2">
    <form class="d-flex" method="get">
      <input class="form-control me-2" type="search" name="q" placeholder="Search units..." value="<?php echo h($search); ?>">
      <button class="btn btn-outline-secondary" type="submit">Search</button>
    </form>
    <a href="unit_new.php" class="btn btn-primary">Add Unit</a>
  </div>
</div>

<div class="table-responsive bg-white rounded shadow-sm">
<table class="table table-sm table-striped align-middle mb-0">
  <thead class="table-light">
    <tr>
      <th>#</th>
      <th>Name</th>
      <th>Abbreviation</th>
      <th>Description</th>
      <th>Created</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    <?php if (empty($units)): ?>
      <tr>
        <td colspan="6" class="text-center text-muted py-4">
          <?php if ($search): ?>
            No units found matching "<?php echo h($search); ?>"
          <?php else: ?>
            No units found. <a href="unit_new.php">Add the first unit</a>
          <?php endif; ?>
        </td>
      </tr>
    <?php else: ?>
      <?php foreach ($units as $unit): ?>
        <tr>
          <td><?php echo (int)$unit['id']; ?></td>
          <td><strong><?php echo h($unit['name']); ?></strong></td>
          <td><span class="badge bg-secondary"><?php echo h($unit['abbreviation']); ?></span></td>
          <td><?php echo h($unit['description']); ?></td>
          <td><?php echo date('M j, Y', strtotime($unit['created_at'])); ?></td>
          <td class="text-nowrap">
            <a class="btn btn-sm btn-outline-primary" href="unit_edit.php?id=<?php echo (int)$unit['id']; ?>">Edit</a>
            <form method="post" action="unit_delete.php" class="d-inline" onsubmit="return confirm('Delete this unit? This action cannot be undone.');">
              <input type="hidden" name="id" value="<?php echo (int)$unit['id']; ?>">
              <button class="btn btn-sm btn-outline-danger">Delete</button>
            </form>
          </td>
        </tr>
      <?php endforeach; ?>
    <?php endif; ?>
  </tbody>
</table>
</div>

<div class="mt-3">
  <small class="text-muted">
    Total: <?php echo count($units); ?> unit(s)
  </small>
</div>

<?php include '_footer.php'; ?>
