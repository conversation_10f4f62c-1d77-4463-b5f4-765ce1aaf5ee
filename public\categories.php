<?php include '_header.php';

$pdo = db();
$search = $_GET['q'] ?? '';
$sql = "SELECT * FROM categories WHERE name LIKE :q ORDER BY name ASC";
$stmt = $pdo->prepare($sql);
$stmt->execute([':q' => '%' . $search . '%']);
$categories = $stmt->fetchAll();
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Equipment Categories</h3>
  <div class="d-flex gap-2">
    <form class="d-flex" method="get">
      <input class="form-control me-2" type="search" name="q" placeholder="Search categories..." value="<?php echo h($search); ?>">
      <button class="btn btn-outline-secondary" type="submit">Search</button>
    </form>
    <a href="category_new.php" class="btn btn-primary">Add Category</a>
  </div>
</div>

<div class="table-responsive bg-white rounded shadow-sm">
<table class="table table-sm table-striped align-middle mb-0">
  <thead class="table-light">
    <tr>
      <th>#</th>
      <th>Category Name</th>
      <th>Description</th>
      <th>Equipment Count</th>
      <th>Created</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    <?php if (empty($categories)): ?>
      <tr>
        <td colspan="6" class="text-center text-muted py-4">
          <?php if ($search): ?>
            No categories found matching "<?php echo h($search); ?>"
          <?php else: ?>
            No categories found. <a href="category_new.php">Add the first category</a>
          <?php endif; ?>
        </td>
      </tr>
    <?php else: ?>
      <?php foreach ($categories as $category): ?>
        <?php
        // Get count of items in this category
        $stmt = $pdo->prepare("SELECT COUNT(*) as item_count FROM items WHERE category_id = ?");
        $stmt->execute([$category['id']]);
        $count = $stmt->fetch()['item_count'];
        ?>
        <tr>
          <td><?php echo (int)$category['id']; ?></td>
          <td><strong><?php echo h($category['name']); ?></strong></td>
          <td><?php echo h($category['description']); ?></td>
          <td>
            <?php if ($count > 0): ?>
              <a href="index.php?category_id=<?php echo (int)$category['id']; ?>" class="badge bg-primary text-decoration-none">
                <?php echo $count; ?> items
              </a>
            <?php else: ?>
              <span class="text-muted">0 items</span>
            <?php endif; ?>
          </td>
          <td><?php echo date('M j, Y', strtotime($category['created_at'])); ?></td>
          <td class="text-nowrap">
            <a class="btn btn-sm btn-outline-primary" href="category_edit.php?id=<?php echo (int)$category['id']; ?>">Edit</a>
            <form method="post" action="category_delete.php" class="d-inline" onsubmit="return confirm('Delete this category? This action cannot be undone.');">
              <input type="hidden" name="id" value="<?php echo (int)$category['id']; ?>">
              <button class="btn btn-sm btn-outline-danger">Delete</button>
            </form>
          </td>
        </tr>
      <?php endforeach; ?>
    <?php endif; ?>
  </tbody>
</table>
</div>

<div class="mt-3">
  <small class="text-muted">
    Total: <?php echo count($categories); ?> categor<?php echo count($categories) === 1 ? 'y' : 'ies'; ?>
  </small>
</div>

<?php include '_footer.php'; ?>
