<?php include '_header.php'; $pdo = db();

$info = '';
if ($_SERVER['REQUEST_METHOD']==='POST' && isset($_FILES['csv']) && is_uploaded_file($_FILES['csv']['tmp_name'])) {
    $path = $_FILES['csv']['tmp_name'];
    $f = fopen($path, 'r');
    $header = fgetcsv($f);
    $count=0;
    while (($row = fgetcsv($f)) !== false) {
        $data = array_combine($header, $row);
        if (!$data) continue;
        // Map your CSV columns to DB fields
        $name = $data['ITEM'] ?? $data['Name'] ?? null;
        if (!$name) continue;
        $brand = $data['BRAND'] ?? null;
        $model = $data['MODEL'] ?? ($data['Unnamed: 3'] ?? null);
        $condition = $data['CONDITION'] ?? null;
        $location = $data['LOCATION'] ?? null;
        $unit_cost = isset($data['UNIT_COST']) ? floatval($data['UNIT_COST']) : 0;
        $pdo->prepare("INSERT INTO items (name,brand,model,condition_status,location,unit_cost) VALUES (?,?,?,?,?,?)")
            ->execute([$name,$brand,$model,$condition,$location,$unit_cost]);
        // If QUANT exists, add initial stock
        if (!empty($data['QUANT'])) {
            $item_id = $pdo->lastInsertId();
            $qty = (int)$data['QUANT'];
            $pdo->prepare("INSERT INTO inventory_transactions (item_id, tx_type, qty, reference, tx_date) VALUES (?,?,?,?,?)")
               ->execute([$item_id,'adjust',$qty,'Initial Import',date('Y-m-d')]);
        }
        $count++;
    }
    fclose($f);
    $info = "Imported $count rows.";
}
?>
<h3>Bulk Import (CSV)</h3>
<div class="alert alert-info">Upload a CSV with columns like: ITEM, BRAND, MODEL/Unnamed: 3, QUANT, CONDITION, LOCATION, UNIT_COST.</div>
<?php if ($info): ?><div class="alert alert-success"><?php echo h($info); ?></div><?php endif; ?>
<form method="post" enctype="multipart/form-data" class="bg-white p-3 rounded shadow-sm">
  <input type="file" name="csv" accept=".csv" class="form-control" required>
  <div class="mt-3">
    <button class="btn btn-primary">Upload & Import</button>
    <a href="index.php" class="btn btn-secondary">Back</a>
  </div>
</form>
<?php include '_footer.php'; ?>