<?php
/**
 * System Test Script
 * This script tests the enhanced inventory system functionality
 */

require_once __DIR__ . '/db.php';

function test_database_structure() {
    echo "Testing database structure...\n";
    
    $pdo = db();
    $tables = ['brands', 'conditions', 'locations', 'units', 'items', 'inventory_transactions'];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✓ Table '$table' exists\n";
        } else {
            echo "✗ Table '$table' missing\n";
            return false;
        }
    }
    
    // Check if items table has foreign key columns
    $stmt = $pdo->query("SHOW COLUMNS FROM items");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_fk_columns = ['brand_id', 'condition_id', 'location_id', 'unit_id'];
    foreach ($required_fk_columns as $column) {
        if (in_array($column, $columns)) {
            echo "✓ Column 'items.$column' exists\n";
        } else {
            echo "✗ Column 'items.$column' missing\n";
            return false;
        }
    }
    
    // Check view
    $stmt = $pdo->query("SHOW TABLES LIKE 'v_item_stock'");
    if ($stmt->rowCount() > 0) {
        echo "✓ View 'v_item_stock' exists\n";
    } else {
        echo "✗ View 'v_item_stock' missing\n";
        return false;
    }
    
    return true;
}

function test_lookup_data() {
    echo "\nTesting lookup data...\n";
    
    $pdo = db();
    $tables = ['brands', 'conditions', 'locations', 'units'];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $result = $stmt->fetch();
        $count = (int)$result['count'];
        
        if ($count > 0) {
            echo "✓ Table '$table' has $count entries\n";
        } else {
            echo "✗ Table '$table' is empty\n";
            return false;
        }
    }
    
    return true;
}

function test_crud_operations() {
    echo "\nTesting CRUD operations...\n";
    
    $pdo = db();
    
    try {
        // Test brand creation
        $stmt = $pdo->prepare("INSERT INTO brands (name, description) VALUES (?, ?)");
        $stmt->execute(['Test Brand', 'Test brand for system testing']);
        $brand_id = $pdo->lastInsertId();
        echo "✓ Brand creation successful (ID: $brand_id)\n";
        
        // Test condition creation
        $stmt = $pdo->prepare("INSERT INTO conditions (name, description) VALUES (?, ?)");
        $stmt->execute(['Test Condition', 'Test condition for system testing']);
        $condition_id = $pdo->lastInsertId();
        echo "✓ Condition creation successful (ID: $condition_id)\n";
        
        // Test location creation
        $stmt = $pdo->prepare("INSERT INTO locations (name, description) VALUES (?, ?)");
        $stmt->execute(['Test Location', 'Test location for system testing']);
        $location_id = $pdo->lastInsertId();
        echo "✓ Location creation successful (ID: $location_id)\n";
        
        // Test unit creation
        $stmt = $pdo->prepare("INSERT INTO units (name, abbreviation, description) VALUES (?, ?, ?)");
        $stmt->execute(['Test Units', 'test', 'Test units for system testing']);
        $unit_id = $pdo->lastInsertId();
        echo "✓ Unit creation successful (ID: $unit_id)\n";
        
        // Test item creation with foreign keys
        $stmt = $pdo->prepare("INSERT INTO items (name, brand_id, condition_id, location_id, unit_id, unit_cost) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['Test Item', $brand_id, $condition_id, $location_id, $unit_id, 10.50]);
        $item_id = $pdo->lastInsertId();
        echo "✓ Item creation with foreign keys successful (ID: $item_id)\n";
        
        // Test view query
        $stmt = $pdo->prepare("SELECT * FROM v_item_stock WHERE item_id = ?");
        $stmt->execute([$item_id]);
        $item = $stmt->fetch();
        
        if ($item && $item['brand'] === 'Test Brand') {
            echo "✓ View query successful - brand name resolved\n";
        } else {
            echo "✗ View query failed - brand name not resolved\n";
            return false;
        }
        
        // Clean up test data
        $pdo->prepare("DELETE FROM items WHERE id = ?")->execute([$item_id]);
        $pdo->prepare("DELETE FROM brands WHERE id = ?")->execute([$brand_id]);
        $pdo->prepare("DELETE FROM conditions WHERE id = ?")->execute([$condition_id]);
        $pdo->prepare("DELETE FROM locations WHERE id = ?")->execute([$location_id]);
        $pdo->prepare("DELETE FROM units WHERE id = ?")->execute([$unit_id]);
        echo "✓ Test data cleanup successful\n";
        
        return true;
        
    } catch (Exception $e) {
        echo "✗ CRUD test failed: " . $e->getMessage() . "\n";
        return false;
    }
}

function test_foreign_key_constraints() {
    echo "\nTesting foreign key constraints...\n";
    
    $pdo = db();
    
    try {
        // Try to insert item with invalid foreign key
        $stmt = $pdo->prepare("INSERT INTO items (name, brand_id) VALUES (?, ?)");
        $stmt->execute(['Test Item', 99999]); // Non-existent brand_id
        
        echo "✗ Foreign key constraint not working - invalid insert succeeded\n";
        return false;
        
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'foreign key constraint') !== false || 
            strpos($e->getMessage(), 'Cannot add or update') !== false) {
            echo "✓ Foreign key constraints working correctly\n";
            return true;
        } else {
            echo "✗ Unexpected error: " . $e->getMessage() . "\n";
            return false;
        }
    }
}

// Run all tests
echo "=== Enhanced Inventory System Test ===\n\n";

$tests = [
    'Database Structure' => 'test_database_structure',
    'Lookup Data' => 'test_lookup_data', 
    'CRUD Operations' => 'test_crud_operations',
    'Foreign Key Constraints' => 'test_foreign_key_constraints'
];

$passed = 0;
$total = count($tests);

foreach ($tests as $test_name => $test_function) {
    echo "Running $test_name test...\n";
    if ($test_function()) {
        $passed++;
        echo "✓ $test_name test PASSED\n\n";
    } else {
        echo "✗ $test_name test FAILED\n\n";
    }
}

echo "=== Test Results ===\n";
echo "Passed: $passed/$total tests\n";

if ($passed === $total) {
    echo "🎉 All tests passed! The enhanced inventory system is working correctly.\n";
    exit(0);
} else {
    echo "❌ Some tests failed. Please check the issues above.\n";
    exit(1);
}
?>
