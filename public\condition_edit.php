<?php include '_header.php'; 

$pdo = db();
$id = (int)($_GET['id'] ?? 0);
$error = '';
$success = '';

// Get the condition
$stmt = $pdo->prepare("SELECT * FROM conditions WHERE id = ?");
$stmt->execute([$id]);
$condition = $stmt->fetch();

if (!$condition) {
    echo '<div class="alert alert-danger">Condition not found.</div>';
    include '_footer.php';
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (empty($name)) {
        $error = 'Condition name is required.';
    } else {
        try {
            $stmt = $pdo->prepare("UPDATE conditions SET name = ?, description = ? WHERE id = ?");
            $stmt->execute([$name, $description, $id]);
            $success = 'Condition updated successfully!';
            // Refresh condition data
            $condition['name'] = $name;
            $condition['description'] = $description;
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry
                $error = 'A condition with this name already exists.';
            } else {
                $error = 'An error occurred while updating the condition.';
            }
        }
    }
}

// Check if condition is used by any items
$stmt = $pdo->prepare("SELECT COUNT(*) as item_count FROM items WHERE condition_id = ?");
$stmt->execute([$id]);
$usage = $stmt->fetch();
$itemCount = (int)$usage['item_count'];
?>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h3 class="mb-0">Edit Condition #<?php echo (int)$condition['id']; ?></h3>
  <a href="conditions.php" class="btn btn-secondary">Back to Conditions</a>
</div>

<?php if ($error): ?>
  <div class="alert alert-danger"><?php echo h($error); ?></div>
<?php endif; ?>

<?php if ($success): ?>
  <div class="alert alert-success"><?php echo h($success); ?></div>
<?php endif; ?>

<form method="post" class="bg-white p-4 rounded shadow-sm">
  <div class="row g-3">
    <div class="col-md-6">
      <label class="form-label">Condition Name *</label>
      <input name="name" required class="form-control" value="<?php echo h($condition['name']); ?>" placeholder="e.g., New, Used, Good, Fair">
      <div class="form-text">Enter a unique condition name</div>
    </div>
    <div class="col-12">
      <label class="form-label">Description</label>
      <textarea name="description" class="form-control" rows="3" placeholder="Optional description about this condition"><?php echo h($condition['description']); ?></textarea>
      <div class="form-text">Optional description or notes about this condition</div>
    </div>
  </div>
  
  <div class="mt-4 d-flex justify-content-between">
    <div>
      <button type="submit" class="btn btn-primary">Update Condition</button>
      <a href="conditions.php" class="btn btn-secondary">Cancel</a>
    </div>
    
    <?php if ($itemCount > 0): ?>
      <div class="text-muted small">
        <i class="fas fa-info-circle"></i> This condition is used by <?php echo $itemCount; ?> item(s)
      </div>
    <?php else: ?>
      <form method="post" action="condition_delete.php" onsubmit="return confirm('Delete this condition? This action cannot be undone.');">
        <input type="hidden" name="id" value="<?php echo (int)$condition['id']; ?>">
        <button class="btn btn-outline-danger">Delete Condition</button>
      </form>
    <?php endif; ?>
  </div>
</form>

<?php include '_footer.php'; ?>
