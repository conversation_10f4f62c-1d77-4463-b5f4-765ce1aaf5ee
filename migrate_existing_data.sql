-- Migration script to convert existing text data to foreign key references
-- Run this AFTER creating the new tables and BEFOR<PERSON> dropping the old columns

-- First, let's add the old columns back temporarily if they don't exist
-- (This handles cases where the database was created with the new schema)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'items' 
     AND column_name = 'brand' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE items ADD COLUMN brand VARCHAR(120) NULL',
    'SELECT "Column brand already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'items' 
     AND column_name = 'condition_status' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE items ADD COLUMN condition_status VARCHAR(80) NULL',
    'SELECT "Column condition_status already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'items' 
     AND column_name = 'location' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE items ADD COLUMN location VARCHAR(160) NULL',
    'SELECT "Column location already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'items' 
     AND column_name = 'unit' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE items ADD COLUMN unit VARCHAR(20) DEFAULT "pcs"',
    'SELECT "Column unit already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Migrate existing brand data
INSERT IGNORE INTO brands (name) 
SELECT DISTINCT brand FROM items WHERE brand IS NOT NULL AND brand != '';

-- Update items table with brand_id
UPDATE items i 
JOIN brands b ON i.brand = b.name 
SET i.brand_id = b.id 
WHERE i.brand IS NOT NULL AND i.brand != '';

-- Migrate existing condition data
INSERT IGNORE INTO conditions (name) 
SELECT DISTINCT condition_status FROM items WHERE condition_status IS NOT NULL AND condition_status != '';

-- Update items table with condition_id
UPDATE items i 
JOIN conditions c ON i.condition_status = c.name 
SET i.condition_id = c.id 
WHERE i.condition_status IS NOT NULL AND i.condition_status != '';

-- Migrate existing location data
INSERT IGNORE INTO locations (name) 
SELECT DISTINCT location FROM items WHERE location IS NOT NULL AND location != '';

-- Update items table with location_id
UPDATE items i 
JOIN locations l ON i.location = l.name 
SET i.location_id = l.id 
WHERE i.location IS NOT NULL AND i.location != '';

-- Migrate existing unit data
INSERT IGNORE INTO units (name, abbreviation) 
SELECT DISTINCT unit, unit FROM items WHERE unit IS NOT NULL AND unit != '';

-- Update items table with unit_id
UPDATE items i 
JOIN units u ON i.unit = u.name 
SET i.unit_id = u.id 
WHERE i.unit IS NOT NULL AND i.unit != '';

-- Set default unit for items without a unit
UPDATE items SET unit_id = (SELECT id FROM units WHERE name = 'Pieces' LIMIT 1) 
WHERE unit_id IS NULL;

-- Optional: Drop the old columns after migration is complete
-- Uncomment these lines after verifying the migration worked correctly
-- ALTER TABLE items DROP COLUMN brand;
-- ALTER TABLE items DROP COLUMN condition_status;
-- ALTER TABLE items DROP COLUMN location;
-- ALTER TABLE items DROP COLUMN unit;
